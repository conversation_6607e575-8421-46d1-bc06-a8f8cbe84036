import PropTypes from 'prop-types';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import Container from '@mui/material/Container';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { fDateTime } from 'src/utils/format-time';

import { useFormResponses } from 'src/apis/useFormResponses';
import { useCdsApi } from 'src/apis/useCdsApi';

import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { EmptyContent } from 'src/components/empty-content';
import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';
import { useSettingsContext } from 'src/components/settings';

export default function FormResponseDetailsView({ id }) {
  const navigate = useNavigate();
  const settings = useSettingsContext();
  const { getFormResponseById } = useFormResponses();
  const { getFormDefinition, loading: cdsLoading, error: cdsError } = useCdsApi();

  const [formResponse, setFormResponse] = useState(null);
  const [formDefinition, setFormDefinition] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Helper function to detect language from text
  const detectLanguage = (text) => {
    if (!text) return 'en';

    // Simple German detection - check for common German words/characters
    const germanIndicators = [
      'für',
      'und',
      'oder',
      'mit',
      'von',
      'zu',
      'in',
      'auf',
      'an',
      'bei',
      'über',
      'unter',
      'zwischen',
      'hinter',
      'vor',
      'neben',
      'aus',
      'nach',
      'durch',
      'um',
      'gegen',
      'ohne',
      'seit',
      'bis',
      'während',
      'wegen',
      'trotz',
      'statt',
      'außer',
      'innerhalb',
      'außerhalb',
      'oberhalb',
      'unterhalb',
      'ihre',
      'ihrer',
      'ihren',
      'ihrem',
      'ihres',
      'ihre',
      'ihr',
      'ihm',
      'ihn',
      'eine',
      'einer',
      'einen',
      'einem',
      'eines',
      'ein',
      'eine',
      'der',
      'die',
      'das',
      'den',
      'dem',
      'des',
      'was',
      'wie',
      'wo',
      'wann',
      'warum',
      'welche',
      'welcher',
      'welches',
      'beschreiben',
      'erklären',
      'zeigen',
      'sagen',
      'geben',
      'nehmen',
      'idee',
      'vorschlag',
      'verbesserung',
      'lösung',
      'problem',
      'chance',
      'nutzen',
      'vorteil',
      'nachteil',
      'möglichkeit',
      'gelegenheit',
    ];

    const textLower = text.toLowerCase();
    const germanWordCount = germanIndicators.filter((word) => textLower.includes(word)).length;

    // Check for German-specific characters (ä, ö, ü, ß)
    const germanChars = (text.match(/[äöüß]/g) || []).length;

    // If more than 1 German word or German characters found, assume German
    if (germanWordCount >= 1 || germanChars >= 1) {
      return 'de';
    }

    return 'en';
  };

  useEffect(() => {
    const fetchFormResponse = async () => {
      try {
        setLoading(true);
        const data = await getFormResponseById(id);
        if (data) {
          setFormResponse(data);

          // Try to fetch form definition from CDS API
          if (data.title) {
            try {
              // Detect language from form title
              const detectedLanguage = detectLanguage(data.title);

              const definition = await getFormDefinition(data.title, detectedLanguage);
              setFormDefinition(definition);
            } catch (cdsError) {
              // Continue without form definition - will use field names as labels
            }
          }
        } else {
          setError('Form response not found');
        }
      } catch (err) {
        setError(err.message || 'Error loading form response');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchFormResponse();
    }
  }, [id, getFormResponseById, getFormDefinition]);

  // Helper function to get field details from CDS API form definition
  const getFieldDetails = useCallback((fieldName) => {
    if (!formDefinition?.topics) {
      return { label: fieldName, topicTitle: 'Form Fields', options: [], type: 'text' };
    }

    // Mapping from German field names to English CDS API field names
    const germanToEnglishFieldMapping = {
      'titel ihrer idee': 'title',
      'beschreiben sie ihre idee': 'description',
      'was ist der wichtigste erwartete business-nutzen ihrer idee? (bitte wählen sie nur eine nutzen-dimension als aus ihrer sicht wichtigste und dominanteste aus)':
        'business_benefit',
      'was ist der unternehmensbereich, auf den sich ihre idee primär auswirkt?': 'business_area',
      'bitte beschreiben sie den nutzen ihrer idee für unser unternehmen so konkret wie möglich.':
        'field_1745186131937',
      'wie viele kollegen in ihrem team / ihrer abteilung oder insgesamt in unserem unternehmen könnten von dieser use case idee profitieren?':
        'department_or_overall_in_our_company',
      'wie hoch ist der erwartete jährliche business-nutzen ihrer idee aus jährlicher finanzieller sicht? (euro, chf oder usd)':
        'business_impact',
      'was sind mögliche hürden oder risiken im zusammenhang mit ihrer idee?': 'possible_hurdles',
    };

    // Try to map German field name to English field name
    const englishFieldName = germanToEnglishFieldMapping[fieldName.toLowerCase()];
    const searchFieldName = englishFieldName || fieldName;

    // Search through topics to find the field using array methods instead of for loops
    const foundTopic = formDefinition.topics?.find(topic => 
      (topic.fields || []).some(field => field.name === searchFieldName)
    );
      
    if (foundTopic) {
      const matchingField = (foundTopic.fields || []).find(field => field.name === searchFieldName);
      return {
        label: matchingField.label || fieldName,
        topicTitle: foundTopic.title || 'Form Fields',
        options: matchingField.options || [],
        type: matchingField.type || 'text',
      };
    }

    // Fallback: Try to use IdeationController's hardcoded mapping for known fields
    const ideationFieldMappings = {
      business_benefit: {
        label: 'What is the most important expected business benefit of your idea?',
        options: [
          { value: '1', label: 'Better customer experience' },
          { value: '2', label: 'Increase in revenues' },
          { value: '3', label: 'Cost reduction' },
          { value: '4', label: 'Better efficiency' },
          { value: '5', label: 'Higher quality' },
          { value: '6', label: 'Better internal decision making' },
          { value: '7', label: 'Better employee satisfaction' },
          { value: '8', label: 'Risk reduction' },
          { value: '9', label: 'New business model' },
          { value: '10', label: 'Other' },
        ],
      },
      business_area: {
        label: 'What is the business area primarily affected by your idea?',
        options: [
          { value: '1', label: 'Marketing' },
          { value: '2', label: 'Sales' },
          { value: '3', label: 'Finance' },
          { value: '4', label: 'Customer care & after sales' },
          { value: '5', label: 'HR' },
          { value: '6', label: 'IT' },
          { value: '7', label: 'R&D' },
          { value: '8', label: 'Supply chain management' },
          { value: '9', label: 'Production' },
          { value: '10', label: 'Quality control' },
          { value: '11', label: 'Maintenance' },
          { value: '12', label: 'Operations & processing' },
          { value: '13', label: 'Risk & compliance' },
          { value: '14', label: 'The entire company' },
          { value: '15', label: 'Other' },
        ],
      },
      time_saved_per_week: {
        label: 'How much time would this save per week?',
        options: [
          { value: '1', label: 'Up to 5 hours per week' },
          { value: '2', label: '5-10 hours per week' },
          { value: '3', label: 'More than 10 hours per week' },
          { value: '4', label: 'Unsure' },
        ],
      },
      department_or_overall_in_our_company: {
        label: 'How many people in your department or overall in our company would benefit?',
        options: [
          { value: '1', label: '1-5' },
          { value: '2', label: '6-10' },
          { value: '3', label: '10-50' },
          { value: '4', label: '50-100' },
          { value: '5', label: "100-1'000" },
          { value: '6', label: ">1'000" },
          { value: '7', label: 'Unsure / not applicable' },
        ],
      },
      business_impact: {
        label: 'What is the expected annual financial business impact?',
        options: [
          { value: '1', label: "Low impact: <100'000 per year" },
          { value: '2', label: "Medium impact: 100'000-1'000'000 per year" },
          { value: '3', label: 'High impact: 1-10 million per year' },
          { value: '4', label: 'Very high impact: above 10 million per year' },
          { value: '5', label: 'No quantitative business impact' },
          { value: '6', label: 'Quantitative business impact hard to quantify' },
        ],
      },
      possible_hurdles: {
        label: 'What are the possible hurdles or risks?',
        options: [
          { value: '1', label: 'Regulatory or compliance risks' },
          { value: '2', label: 'Financial risks or budget constraints' },
          { value: '3', label: 'Technical dependencies and limitations' },
          { value: '4', label: 'Market competition or customer adoption risks' },
          { value: '5', label: 'Resource and staffing constraints' },
          { value: '6', label: 'No significant hurdles or risks' },
          { value: '7', label: 'Other' },
        ],
      },
    };

    // Check if this is a known ideation field (try both German and English field names)
    const fallbackFieldName = englishFieldName || fieldName;
    if (ideationFieldMappings[fallbackFieldName]) {
      const mapping = ideationFieldMappings[fallbackFieldName];
      return {
        label: mapping.label,
        topicTitle: 'Ideation Form',
        options: mapping.options,
        type: 'select',
      };
    }

    return { label: fieldName, topicTitle: 'Form Fields', options: [], type: 'text' };
  }, [formDefinition]);

  // Helper function to get option label from value
  const getOptionLabel = (value, options, fieldName = '') => {
    if (!value) return 'No response provided';
    if (!options || !Array.isArray(options)) return value;

    // Convert value to string for consistent comparison
    const stringValue = String(value).trim();

    // Find matching option with multiple comparison strategies
    const option = options.find((opt) => {
      if (!opt) return false;

      const optValue = String(opt.value || '').trim();
      const optLabel = String(opt.label || '').trim();

      // Exact match
      if (optValue === stringValue) return true;

      // Case-insensitive match
      if (optValue.toLowerCase() === stringValue.toLowerCase()) return true;

      // Label match (in case value was stored as label)
      if (optLabel.toLowerCase() === stringValue.toLowerCase()) return true;

      return false;
    });

    if (option) {
      return option.label;
    }

    // Fallback: Try to infer from field name and value patterns
    const fieldNameLower = fieldName.toLowerCase();

    // Rating scale patterns (1-10)
    if (
      (fieldNameLower.includes('rating') || fieldNameLower.includes('bewertung')) &&
      /^[1-9]$|^10$/.test(stringValue)
    ) {
      return `${stringValue}/10`;
    }

    // Yes/No patterns
    if (stringValue === '1' && (fieldNameLower.includes('yes') || fieldNameLower.includes('ja'))) {
      return 'Yes';
    }
    if (stringValue === '0' && (fieldNameLower.includes('no') || fieldNameLower.includes('nein'))) {
      return 'No';
    }

    // Relevance patterns (German)
    if (fieldNameLower.includes('relevanz')) {
      const relevanceMap = {
        10: 'Sehr hoch',
        7: 'Hoch',
        5: 'Mittel',
        3: 'Niedrig',
        1: 'Sehr niedrig',
      };
      if (relevanceMap[stringValue]) {
        return relevanceMap[stringValue];
      }
    }

    // Comprehension patterns (German)
    if (fieldNameLower.includes('verständlichkeit')) {
      const comprehensionMap = {
        10: 'Sehr gut',
        7: 'Gut',
        5: 'Befriedigend',
        3: 'Ausreichend',
        1: 'Mangelhaft',
      };
      if (comprehensionMap[stringValue]) {
        return comprehensionMap[stringValue];
      }
    }

    return value;
  };

  // Group responses by topic
  const groupedResponses = useMemo(() => {
    if (!formResponse?.responses) return {};

    const groups = {};

    formResponse.responses.forEach((response) => {
      const { topicTitle, label, options, type } = getFieldDetails(response.name);

      if (!groups[topicTitle]) {
        groups[topicTitle] = [];
      }

      groups[topicTitle].push({
        ...response,
        displayLabel: label || response.name,
        fieldOptions: options,
        fieldType: type,
      });
    });

    return groups;
  }, [formResponse?.responses, getFieldDetails]);

  const getFormTypeLabel = (type) => {
    switch (type) {
      case 'help':
        return 'Help';
      case 'feedback':
        return 'Feedback';
      case 'survey':
        return 'Survey';
      default:
        return type;
    }
  };

  const renderResponse = (response) => {
    if (response.type === 'file' && response.value) {
      return (
        <Button
          variant="outlined"
          size="small"
          startIcon={<Iconify icon="eva:file-text-outline" />}
          onClick={() => window.open(response.value, '_blank')}
          sx={{ textTransform: 'none' }}
        >
          View File
        </Button>
      );
    }

    if (response.type === 'checkbox' && Array.isArray(response.value)) {
      return (
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {response.value.map((item, index) => {
            const displayValue = getOptionLabel(item, response.fieldOptions, response.name);
            return (
              <Chip
                key={index}
                label={displayValue}
                size="small"
                variant="filled"
                color="primary"
                sx={{
                  bgcolor: 'primary.lighter',
                  color: 'primary.darker',
                  fontWeight: 500,
                  fontSize: '0.75rem',
                }}
              />
            );
          })}
        </Stack>
      );
    }

    if (response.type === 'textarea' || (response.value && response.value.length > 100)) {
      return (
        <Box
          sx={{
            p: 1.5,
            bgcolor: 'background.paper',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'divider',
            maxHeight: 120,
            overflow: 'auto',
          }}
        >
          <Typography variant="body2" sx={{ color: 'text.primary', lineHeight: 1.5 }}>
            {response.value || 'No response provided'}
          </Typography>
        </Box>
      );
    }

    // For radio, select, and other types with options
    const displayValue = getOptionLabel(response.value, response.fieldOptions, response.name);

    return (
      <Box
        sx={{
          p: 1.5,
          bgcolor: 'primary.lighter',
          borderRadius: 1,
          border: '1px solid',
          borderColor: 'primary.light',
          textAlign: 'center',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            color: 'primary.darker',
            fontWeight: 600,
            fontSize: '1.25rem',
          }}
        >
          {displayValue || 'No response provided'}
        </Typography>
        {/* Show both label and raw value if they're different */}
        {displayValue !== response.value && response.value && (
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              display: 'block',
              mt: 0.5,
            }}
          >
            (Value: {response.value})
          </Typography>
        )}
      </Box>
    );
  };

  if (loading || cdsLoading) {
    return (
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 5 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error || !formResponse) {
    return (
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <EmptyContent
          title="Form Response Not Found"
          description={
            error || 'The requested form response was not found or you do not have access.'
          }
        />
      </Container>
    );
  }

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        links={[
          { name: 'Home', href: '/' },
          { name: 'Form Responses', href: '/form-responses' },
          { name: 'Details' },
        ]}
        action={
          <Button
            variant="contained"
            startIcon={<Iconify icon="eva:arrow-back-fill" />}
            onClick={() => navigate('/form-responses')}
          >
            Go Back
          </Button>
        }
        sx={{ mb: 2 }}
      />

      <Grid container spacing={2}>
        {/* Left side - Form and Submitter Information */}
        <Grid item xs={12} md={4}>
          <Stack spacing={2}>
            {/* Form Information */}
            <Card sx={{ height: 'fit-content' }}>
              <CardContent sx={{ pb: '16px !important' }}>
                <Typography variant="h5" gutterBottom sx={{ mb: 1 }}>
                  {formResponse.title || 'Untitled Form'}
                </Typography>

                <Typography
                  variant="body2"
                  sx={{ color: 'text.secondary', mb: 2, lineHeight: 1.4 }}
                >
                  {formResponse.description || 'No description available'}
                </Typography>

                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2,
                  }}
                >
                  <Label variant="soft" color="primary">
                    {getFormTypeLabel(formResponse.formType)}
                  </Label>
                  <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                    {fDateTime(formResponse.createdAt)}
                  </Typography>
                </Box>

                <Divider sx={{ my: 1.5 }} />

                {/* Submitter Information */}
                <Typography variant="subtitle2" gutterBottom sx={{ mb: 1 }}>
                  Submitted By
                </Typography>
                <Stack direction="row" spacing={1.5} alignItems="center">
                  <Avatar sx={{ width: 40, height: 40 }}>
                    {formResponse.submittedBy?.name?.charAt(0).toUpperCase()}
                  </Avatar>
                  <Box sx={{ minWidth: 0, flex: 1 }}>
                    <Typography variant="subtitle2" noWrap>
                      {formResponse.submittedBy?.name || 'Unknown User'}
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }} noWrap>
                      {formResponse.submittedBy?.email || 'Email not found'}
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Stack>
        </Grid>

        {/* Right side - Form Responses */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: 'fit-content' }}>
            <CardContent sx={{ pb: '16px !important' }}>
              <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
                Form Responses ({formResponse.responses?.length || 0})
              </Typography>

              {Object.keys(groupedResponses).length > 0 ? (
                <Stack spacing={3}>
                  {Object.entries(groupedResponses).map(([topicTitle, responses]) => (
                    <Box key={topicTitle}>
                      <Typography
                        variant="h6"
                        sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}
                      >
                        {topicTitle}
                      </Typography>
                      <Stack spacing={2}>
                        {responses.map((response, index) => (
                          <Box
                            key={index}
                            sx={{
                              p: 2,
                              bgcolor: 'background.neutral',
                              borderRadius: 1,
                              border: '1px solid',
                              borderColor: 'divider',
                            }}
                          >
                            <Stack
                              direction="row"
                              justifyContent="space-between"
                              alignItems="flex-start"
                              sx={{ mb: 1 }}
                            >
                              <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                                {response.displayLabel}
                                {response.required && (
                                  <Typography
                                    component="span"
                                    sx={{ color: 'error.main', ml: 0.5 }}
                                  >
                                    *
                                  </Typography>
                                )}
                              </Typography>
                              <Chip
                                label={response.type}
                                size="small"
                                variant="outlined"
                                sx={{ textTransform: 'capitalize', fontSize: '0.75rem' }}
                              />
                            </Stack>
                            <Box sx={{ mt: 1 }}>{renderResponse(response)}</Box>
                          </Box>
                        ))}
                      </Stack>
                    </Box>
                  ))}
                </Stack>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Iconify
                    icon="solar:file-text-bold-duotone"
                    sx={{ width: 48, height: 48, color: 'text.disabled', mb: 2 }}
                  />
                  <Typography variant="h6" sx={{ color: 'text.secondary', mb: 1 }}>
                    No Responses Found
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.disabled' }}>
                    No responses have been provided for this form yet.
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}

FormResponseDetailsView.propTypes = {
  id: PropTypes.string,
};
