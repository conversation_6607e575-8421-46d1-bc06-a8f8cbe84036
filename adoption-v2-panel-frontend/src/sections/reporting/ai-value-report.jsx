import { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';

import { Box, Dialog, DialogContent, DialogTitle } from '@mui/material';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import {
  DataGrid,
  gridClasses,
  GridToolbarContainer,
  GridToolbarQuickFilter,
  GridToolbarFilterButton,
} from '@mui/x-data-grid';

import { useRouter } from 'src/routes/hooks';

import { useBoolean } from 'src/hooks/use-boolean';
import { useSetState } from 'src/hooks/use-set-state';

import { useTranslate } from 'src/locales';
import { useReports } from 'src/apis/useReports';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { EmptyContent } from 'src/components/empty-content';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { RenderC<PERSON>IdeaName, RenderCellExcelDownloadButton } from './reports-table-row';

// ----------------------------------------------------------------------

const PUBLISH_OPTIONS = [
  { value: 'published', label: 'Published' },
  { value: 'draft', label: 'Draft' },
];

const HIDE_COLUMNS_TOGGLABLE = ['category', 'actions'];

// Helper function for section titles
const getSectionTitle = (sectionKey) => {
  const titleMap = {
    introductionCourseSheet1: 'Beginner Journey',
    jobSpecificCourseSheet1: 'Expert Journey',
    beginnerJourneyProductivitySurvey: 'Beginner journey - AI adoption & productivity survey',
    expertJourneyFirstHalf: 'Expert journey / first half',
    expertJourneySecondHalf: 'Expert journey / second half',
    masterJourneyFirstHalf: 'Master journey / first half',
    masterJourneySecondHalf: 'Master journey / second half',
  };
  return titleMap[sectionKey] || sectionKey;
};

// ----------------------------------------------------------------------

export const AIValueReport = forwardRef((props, ref) => {
  const confirmRows = useBoolean();
  const { t } = useTranslate();

  const router = useRouter();

  const filters = useSetState({ publish: [], stock: [] });

  const [tableData, setTableData] = useState([]);
  const [selectedRowIds, setSelectedRowIds] = useState([]);
  const [filterButtonEl, setFilterButtonEl] = useState(null);
  const { getReportsByType } = useReports();
  const [previewData, setPreviewData] = useState(null);
  const [openPreview, setOpenPreview] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    const fetchReports = async () => {
      try {
        const response = await getReportsByType('ai-value');

        const formattedReports = response.map((report) => ({
          id: report._id,
          reportName: report.companyName || report.company || 'N/A',
          fileName: report.downloadLink || 'not available',
          createdAt: report.createdAt,
          reportData: report.reportData,
        }));

        setTableData(formattedReports);
      } catch (error) {
        console.error('Error fetching AI value reports:', error);
        toast.error(t('reporting.Failed to load reports. Please try again.'));
        setTableData([]); // Show empty table on error
      }
    };
    fetchReports();
  }, [refreshKey, getReportsByType, t]);

  const canReset = filters.state.publish.length > 0 || filters.state.stock.length > 0;

  const dataFiltered = applyFilter({ inputData: tableData, filters: filters.state });

  const handleDeleteRows = useCallback(() => {
    const deleteRows = tableData.filter((row) => !selectedRowIds.includes(row.id));

    toast.success('Delete success!');

    setTableData(deleteRows);
  }, [selectedRowIds, tableData]);

  const handleDeleteRow = useCallback(
    (id) => {
      const deleteRow = tableData.filter((row) => row.id !== id);

      toast.success('Delete success!');

      setTableData(deleteRow);
    },
    [tableData]
  );

  const CustomToolbarCallback = useCallback(
    () => (
      <CustomToolbar
        filters={filters}
        canReset={canReset}
        selectedRowIds={selectedRowIds}
        setFilterButtonEl={setFilterButtonEl}
        filteredResults={dataFiltered.length}
        onOpenConfirmDeleteRows={confirmRows.onTrue}
      />
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filters.state, selectedRowIds]
  );

  const handlePreviewClose = () => {
    setOpenPreview(false);
    setPreviewData(null);
  };

  const handlePreviewOpen = (data) => {
    setPreviewData(data);
    setOpenPreview(true);
  };

  const handleReportGenerated = useCallback(() => {
    setRefreshKey((prev) => prev + 1);
  }, []);

  const columns = [
    {
      field: 'reportName',
      headerName: t('reporting.Company Name'),
      flex: 1,
      renderCell: (params) => <RenderCellIdeaName params={params} />,
    },
    {
      field: 'createdAt',
      headerName: t('reporting.Created At'),
      flex: 1,
      type: 'date',
      valueGetter: (params) => {
        if (!params || !params.row || !params.row.createdAt) return null;
        return new Date(params.row.createdAt);
      },
      renderCell: (params) => {
        if (!params || !params.row || !params.row.createdAt) return 'No date';
        try {
          const date = new Date(params.row.createdAt);
          if (Number.isNaN(date.getTime())) return `Invalid date`;

          const day = date.getDate().toString().padStart(2, '0');
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const year = date.getFullYear();
          const hours = date.getHours().toString().padStart(2, '0');

          return <Box>{`${day}/${month}/${year} ${hours}:00`}</Box>;
        } catch (error) {
          return `Date error`;
        }
      },
    },
    {
      field: 'preview',
      headerName: t('reporting.Preview'),
      flex: 1,
      headerAlign: 'center',
      renderCell: (params) => (
        <Box sx={{ textAlign: 'center', width: '100%' }}>
          <Button
            variant="outlined"
            size="small"
            onClick={() => handlePreviewOpen(params.row.reportData)}
            startIcon={<Iconify icon="eva:eye-fill" />}
          >
            {t('Preview')}
          </Button>
        </Box>
      ),
    },
    {
      field: 'fileName',
      headerName: t('reporting.Download'),
      flex: 1,
      headerAlign: 'center',
      renderCell: (params) => (
        <Box sx={{ textAlign: 'center', width: '100%' }}>
          <RenderCellExcelDownloadButton reportId={params.row.id} />
        </Box>
      ),
    },
  ];

  const getTogglableColumns = () =>
    columns
      .filter((column) => !HIDE_COLUMNS_TOGGLABLE.includes(column.field))
      .map((column) => column.field);

  useImperativeHandle(ref, () => ({
    handleReportGenerated: () => {
      setRefreshKey((prev) => prev + 1);
    },
  }));

  return (
    <Box sx={{ ml: -3 }}>
      <DataGrid
        rows={dataFiltered}
        disableSelectionOnClick
        disableMultipleRowSelection
        disableRowSelectionOnClick
        disableColumnMenu
        disableMultipleSelection
        getRowHeight={() => '60'}
        columns={columns.map((column) => ({
          ...column,
          flex: column.flex || 1,
        }))}
        pageSizeOptions={[5, 10, 25]}
        initialState={{
          pagination: { paginationModel: { pageSize: 10 } },
          sorting: {
            sortModel: [{ field: 'createdAt', sort: 'desc' }],
            sortingOrder: ['desc', 'asc'],
          },
        }}
        slots={{
          toolbar: CustomToolbarCallback,
          noRowsOverlay: () => <EmptyContent />,
          noResultsOverlay: () => <EmptyContent title={t('ideation.No results found')} />,
        }}
        slotProps={{
          panel: { anchorEl: filterButtonEl },
          toolbar: { setFilterButtonEl },
          columnsManagement: { getTogglableColumns },
        }}
        sx={{ [`& .${gridClasses.cell}`]: { alignItems: 'center', display: 'inline-flex' } }}
      />

      <Dialog open={openPreview} onClose={handlePreviewClose} maxWidth="xl" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="eva:file-text-outline" />
            AI Value Report Preview
            <Box sx={{ ml: 'auto' }}>
              <Button
                variant="outlined"
                size="small"
                onClick={handlePreviewClose}
                startIcon={<Iconify icon="eva:close-fill" />}
              >
                Close
              </Button>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {previewData && previewData.excelData && previewData.excelData.sections ? (
            <Box sx={{ height: 600, width: '100%' }}>
              <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider', bgcolor: 'grey.50' }}>
                <Box sx={{ typography: 'h6', color: 'primary.main' }}>AI Value Report Preview</Box>
                {previewData.overallStats && (
                  <Box
                    sx={{
                      mt: 2,
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
                      gap: 2,
                    }}
                  >
                    <Box sx={{ textAlign: 'center' }}>
                      <Box sx={{ typography: 'h6' }}>
                        {previewData.overallStats.totalCompanyUsers || 0}
                      </Box>
                      <Box sx={{ typography: 'caption', color: 'text.secondary' }}>
                        Company Users
                      </Box>
                    </Box>
                    <Box sx={{ textAlign: 'center' }}>
                      <Box sx={{ typography: 'h6' }}>
                        {previewData.overallStats.totalRespondents || 0}
                      </Box>
                      <Box sx={{ typography: 'caption', color: 'text.secondary' }}>Respondents</Box>
                    </Box>
                    <Box sx={{ textAlign: 'center' }}>
                      <Box sx={{ typography: 'h6' }}>
                        {previewData.excelData?.sections
                          ? Object.values(previewData.excelData.sections).reduce(
                              (total, section) => total + (section.questions?.length || 0),
                              0
                            )
                          : 0}
                      </Box>
                      <Box sx={{ typography: 'caption', color: 'text.secondary' }}>
                        Total Questions
                      </Box>
                    </Box>
                  </Box>
                )}
              </Box>

              <Box sx={{ height: 'calc(600px - 120px)', overflow: 'auto', p: 3 }}>
                {Object.entries(previewData.excelData.sections).map(([sectionKey, sectionData]) => {
                  const sectionTitle = getSectionTitle(sectionKey);

                  return (
                    <Box key={sectionKey} sx={{ mb: 4 }}>
                      {/* Section Header */}
                      <Box
                        sx={{
                          mb: 2,
                          p: 2,
                          bgcolor: '#FFE6E6FA',
                          borderRadius: 1,
                          typography: 'h6',
                          fontWeight: 'bold',
                        }}
                      >
                        {sectionTitle}
                      </Box>

                      {/* Questions */}
                      {sectionData.questions &&
                        sectionData.questions.map((question, questionIndex) => (
                          <Box
                            key={questionIndex}
                            sx={{ mb: 3, border: 1, borderColor: 'divider', borderRadius: 1 }}
                          >
                            {/* Question Header */}
                            <Box
                              sx={{
                                p: 2,
                                bgcolor: '#DEE9F8',
                                fontWeight: 'bold',
                                borderBottom: 1,
                                borderColor: 'divider',
                              }}
                            >
                              {`${questionIndex + 1}. ${question.questionText}`}
                            </Box>

                            {/* Question Data Table */}
                            <Box sx={{ p: 2 }}>
                              {/* Table Headers */}
                              <Box
                                sx={{
                                  display: 'grid',
                                  gridTemplateColumns: '2fr 1fr 1fr',
                                  gap: 1,
                                  p: 1,
                                  bgcolor: 'grey.100',
                                  fontWeight: 'bold',
                                  fontSize: '0.875rem',
                                }}
                              >
                                <Box>Options</Box>
                                <Box sx={{ textAlign: 'center' }}>Count</Box>
                                <Box sx={{ textAlign: 'center' }}>%</Box>
                              </Box>

                              {/* Data Rows */}
                              {question.companyData &&
                                Object.entries(question.companyData).map(
                                  ([option, data], index) => (
                                    <Box
                                      key={index}
                                      sx={{
                                        display: 'grid',
                                        gridTemplateColumns: '2fr 1fr 1fr',
                                        gap: 1,
                                        p: 1,
                                        borderBottom: 1,
                                        borderColor: 'divider',
                                        bgcolor: index % 2 === 0 ? 'transparent' : '#DEE9F8',
                                      }}
                                    >
                                      <Box sx={{ fontSize: '0.875rem' }}>{option}</Box>
                                      <Box sx={{ textAlign: 'center', fontSize: '0.875rem' }}>
                                        {data.count || 0}
                                      </Box>
                                      <Box sx={{ textAlign: 'center', fontSize: '0.875rem' }}>
                                        {data.percentage || '0%'}
                                      </Box>
                                    </Box>
                                  )
                                )}

                              {/* Average Row if exists */}
                              {question.hasAverage && (
                                <Box
                                  sx={{
                                    display: 'grid',
                                    gridTemplateColumns: '2fr 2fr',
                                    gap: 1,
                                    p: 1,
                                    bgcolor: '#FFE6E6FA',
                                    fontWeight: 'bold',
                                    mt: 1,
                                  }}
                                >
                                  <Box>Average</Box>
                                  <Box sx={{ textAlign: 'center' }}>
                                    {question.averageValue || 'N/A'}
                                  </Box>
                                </Box>
                              )}
                            </Box>
                          </Box>
                        ))}
                    </Box>
                  );
                })}
              </Box>
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center' }}>
              <Iconify
                icon="eva:alert-circle-outline"
                sx={{ fontSize: 48, color: 'warning.main', mb: 2 }}
              />
              <Box sx={{ typography: 'h6', mb: 1 }}>No Data Available</Box>
              <Box sx={{ typography: 'body2', color: 'text.secondary' }}>
                The report data is empty or not available for preview.
              </Box>
            </Box>
          )}
        </DialogContent>
      </Dialog>

      <ConfirmDialog
        open={confirmRows.value}
        onClose={confirmRows.onFalse}
        title={t('Delete')}
        content={<>{t('ideation.Are you sure want to delete items?')}</>}
        action={
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              handleDeleteRows();
              confirmRows.onFalse();
            }}
          >
            {t('Delete')}
          </Button>
        }
      />
    </Box>
  );
});

AIValueReport.displayName = 'AIValueReport';

function CustomToolbar({ selectedRowIds, setFilterButtonEl, onOpenConfirmDeleteRows }) {
  const { t } = useTranslate();
  return (
    <GridToolbarContainer sx={{ mt: -3 }}>
      <GridToolbarQuickFilter sx={{ ml: -2 }} />
      <Stack spacing={1} flexGrow={1} direction="row" alignItems="center" justifyContent="flex-end">
        {!!selectedRowIds.length && (
          <Button
            size="small"
            color="error"
            startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
            onClick={onOpenConfirmDeleteRows}
          >
            {t('Delete')} ({selectedRowIds.length})
          </Button>
        )}

        <GridToolbarFilterButton ref={setFilterButtonEl} />
        {/* <GridToolbarExport /> */}
      </Stack>
    </GridToolbarContainer>
  );
}

function applyFilter({ inputData, filters }) {
  const { stock, publish } = filters;

  if (stock.length) {
    inputData = inputData.filter((product) => stock.includes(product.inventoryType));
  }

  if (publish.length) {
    inputData = inputData.filter((product) => publish.includes(product.publish));
  }

  return inputData;
}
