const JourneyTrackingModel =
  require("../../models/TrackingModel").JourneyTrackingModel;
const UserModel = require("../../models/UserModel");
const FormResponse = require("../../models/FormResponse");
const CompanyModel = require("../../models/CompanyModel");
const ReportModel = require("../../models/ReportModel");
const mongoose = require("mongoose");
const axios = require("axios");
const config = require("../../../config");

// Az-content-api configuration
const AZ_CONTENT_API_URL = config.CDS_URL || "http://localhost:3001/api/v1";
const CDS_API_KEY = config.CDS_API_KEY || "";

exports.generateAIValueReport = async ({ companyId }) => {
  /**
   * Course IDs for different AI learning levels
   */
  const introCourseId = "67d0363ebbac7c0b9ed65da4";

  // AI Foundations Course IDs
  const aiFoundationsCourseIds = [
    "6824857a4a9efb3e480858fe",
    "6824956e4a9efb3e4808e607",
    "68249bad4a9efb3e4809321a",
    "68249c914a9efb3e4809636d",
    "68249d4e4a9efb3e48099615",
    "68249eb34a9efb3e4809c487",
    "68249f6f4a9efb3e4809f2f2",
    "68249ffe4a9efb3e480a2161",
    "6824a13c4a9efb3e480a4fdf",
    "6824a1bc4a9efb3e480a8c0d",
    "682595684a9efb3e480b71ea",
    "682596c04a9efb3e480bc7dc",
    "6826e3434a9efb3e480e11a4",
    "6826e42a4a9efb3e480e5afc",
    "6826e4d14a9efb3e480e8974",
    "6826e54d4a9efb3e480eb7e8",
    "6826e5b44a9efb3e480ee64b",
  ];

  // AI Expert Course IDs
  const jobSpecificCourseIds = [
    "67d18e63f5037179194a6932",
    "67d296cef138f4f478e6fbea",
    "67d29879f138f4f478e7cbcc",
    "67d29e85f138f4f478e8cec8",
    "67d2a186f138f4f478e9bc8b",
    "67d2b7a7f138f4f478ec06a1",
    "67d2bc4bf138f4f478eca693",
    "67d2bd89f138f4f478ed272f",
    "67d2c1a3f138f4f478edd401",
    "67d2c431f138f4f478ef656f",
    "67d2cb42f138f4f478f344fb",
    "67d2d63cf138f4f478f6aff8",
    "67d2dbcaf138f4f478f9496d",
    "67d2df9ff138f4f478fb3720",
    "67d2e6edf138f4f478fd0318",
    "67d2ec1ff138f4f478fffa94",
    "67d7f3cb2238849a6dbfaa48",
  ];

  const aiValueAssessmentExpertIds = [
    "67d2c88cf138f4f478f12081",
    "67d1a078f5037179194d58b4",
    "67d296daf138f4f478e70433",
    "67d2c7d3f138f4f478f0aed0",
    "67d2a2ebf138f4f478ea357f",
    "67d2c179f138f4f478eda687",
    "67d1a0fff5037179194d8a5a",
    "67d19e7ef5037179194cf6d0",
    "67d2c3f1f138f4f478ef3120",
    "67d2a185f138f4f478e9b1de",
    "67d2a490f138f4f478eac7ad",
    "67d29f57f138f4f478e917a3",
    "67d29849f138f4f478e7b0ad",
    "67d2c1e5f138f4f478ee1d6a",
    "67d2a3c2f138f4f478ea8ea8",
    "67d2c24bf138f4f478ee76f4",
    "67d29d64f138f4f478e8a18e",
    "67d19379f5037179194b30ea",
  ];
  const proactivityExpertId = "67d16c2abbac7c0b9ee78c43";

  const masterClassMasterIds = [
    "67d2d18ff138f4f478f44397",
    "67d2d298f138f4f478f4c5af",
    "67d2d3fef138f4f478f577a0",
    "67d2d53af138f4f478f5fbea",
    "67d2d5e4f138f4f478f665e1",
    "67d2d659f138f4f478f6d0aa",
    "67d2d77ef138f4f478f7a6cc",
    "67d2d812f138f4f478f7f199",
    "67d2dbe4f138f4f478f97157",
    "67d2ddc3f138f4f478fa64e4",
    "67d2de5df138f4f478faaa37",
    "67d2df07f138f4f478faf0bf",
    "67d2dfb3f138f4f478fb6193",
    "67d2e019f138f4f478fbd2a7",
    "67d2e996f138f4f478fe1246",
    "67d2ea02f138f4f478fe8eab",
    "67d2ea72f138f4f478fef251",
  ];

  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "ai-value",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }

    // Ensure mongoose connection is ready
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(config.MONGODB_URI, {
        connectTimeoutMS: 5000,
        serverSelectionTimeoutMS: 5000,
      });
    }

    // Find all users in the company with timeout
    const users = await UserModel.find({
      company: new mongoose.Types.ObjectId(companyId),
    }).maxTimeMS(5000);
    const company = await CompanyModel.findById(companyId);

    if (!users || users.length === 0) {
      return {
        reportType: "ai-value",
        status: "error",
        message: "No users found for this company",
        data: null,
      };
    }

    // Create date range for filtering (get all data from last 2 years)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setFullYear(endDate.getFullYear() - 2);

    // Get all user IDs for the company
    const userIds = users.map((user) => user._id);

    // Function to analyze form responses for specific courses and form titles
    const analyzeFormResponsesByCourse = async (
      courseIds,
      questionFilter = null,
      formTitleFilter = null
    ) => {
      // courseIds can be a single ID or array of IDs
      // questionFilter: { type: 'range', start: 0, end: 2 } or { type: 'specific', indices: [0, 2] }
      // formTitleFilter: specific form title to filter by (e.g., "AI adoption survey")
      const courseIdArray = Array.isArray(courseIds) ? courseIds : [courseIds];

      // Convert string IDs to ObjectIds
      const objectIds = courseIdArray.map(
        (id) => new mongoose.Types.ObjectId(id)
      );

      // Build query - start with basic course filter
      const query = {
        submittedBy: { $in: userIds },
        source: "course-content",
        "sourceDetails.courseId": { $in: objectIds },
        createdAt: { $gte: startDate, $lte: endDate },
      };

      // Add form title filter if specified
      if (formTitleFilter) {
        query.title = { $regex: formTitleFilter, $options: "i" };
      }

      const formResponses = await FormResponse.find(query).populate(
        "submittedBy",
        "name email department companyDepartment role company"
      );

      if (formResponses.length === 0) {
        return null;
      }

      // Get unique course IDs to fetch course data and extract form_ids
      const uniqueCourseIds = [
        ...new Set(
          formResponses
            .map((response) => response.sourceDetails?.courseId?.toString())
            .filter(Boolean)
        ),
      ];

      console.log(
        `[DEBUG] Found ${uniqueCourseIds.length} unique course IDs:`,
        uniqueCourseIds.slice(0, 3)
      );

      // Fetch course data and extract form_ids from contentBlocks
      const formSchemas = {};
      const courseToFormMapping = {}; // courseId -> [formIds]

      for (const courseId of uniqueCourseIds) {
        console.log(`[DEBUG] Fetching course data for ID: ${courseId}`);
        try {
          const courseResponse = await axios.get(
            `${AZ_CONTENT_API_URL}/courses/${courseId}`,
            {
              headers: {
                "Content-Type": "application/json",
                ...(CDS_API_KEY && { "x-api-key": CDS_API_KEY }),
              },
              timeout: 5000,
            }
          );

          if (
            courseResponse.data?.status === "success" &&
            courseResponse.data?.data
          ) {
            const courseData = courseResponse.data.data;
            const formIds = [];

            // Extract form_ids from course structure (check translations)
            let chapters = [];
            if (courseData.translations?.en?.chapters) {
              chapters = courseData.translations.en.chapters;
            } else if (courseData.chapters) {
              chapters = courseData.chapters;
            }

            chapters.forEach((chapter) => {
              if (chapter.topics) {
                chapter.topics.forEach((topic) => {
                  if (topic.contentBlocks) {
                    topic.contentBlocks.forEach((block) => {
                      if (block.type === "form" && block.form_id) {
                        formIds.push(block.form_id);
                      }
                    });
                  }
                });
              }
            });

            courseToFormMapping[courseId] = formIds;
            console.log(
              `[DEBUG] Course ${courseId} has ${formIds.length} forms:`,
              formIds
            );

            // Now fetch form schemas for each form_id
            for (const formId of formIds) {
              console.log(`[DEBUG] Fetching form schema for ID: ${formId}`);
              try {
                const formResponse = await axios.get(
                  `${AZ_CONTENT_API_URL}/forms/list/${formId}`,
                  {
                    headers: {
                      "Content-Type": "application/json",
                      ...(CDS_API_KEY && { "x-api-key": CDS_API_KEY }),
                    },
                    timeout: 5000,
                  }
                );

                if (
                  formResponse.data?.status === "success" &&
                  formResponse.data?.data
                ) {
                  console.log(
                    `[DEBUG] Form schema found for ${formId}, topics: ${
                      formResponse.data.data.topics?.length || 0
                    }`
                  );
                  formSchemas[formId] = formResponse.data.data;
                } else {
                  console.log(`[DEBUG] No form schema found for ${formId}`);
                }
              } catch (error) {
                console.log(
                  `[DEBUG] Error fetching form ${formId}:`,
                  error.message
                );
              }
            }
          } else {
            console.log(`[DEBUG] No course data found for ${courseId}`);
          }
        } catch (error) {
          console.log(
            `[DEBUG] Error fetching course ${courseId}:`,
            error.message
          );
        }
      }

      console.log(
        `[DEBUG] Total form schemas loaded: ${Object.keys(formSchemas).length}`
      );

      // Analyze responses by questions
      const questionAnalysis = {};

      // First, get all questions from form schemas
      Object.values(formSchemas).forEach((formSchema) => {
        if (formSchema) {
          // Use translations.en.topics if available, otherwise use main topics
          let topicsToProcess = [];
          if (formSchema.translations?.en?.topics) {
            topicsToProcess = formSchema.translations.en.topics;
          } else if (formSchema.topics) {
            topicsToProcess = formSchema.topics;
          }

          // Apply question filter if provided
          if (questionFilter) {
            if (questionFilter.type === "range") {
              topicsToProcess = topicsToProcess.slice(
                questionFilter.start,
                questionFilter.end
              );
            } else if (questionFilter.type === "specific") {
              topicsToProcess = questionFilter.indices
                .map((index) => topicsToProcess[index])
                .filter(Boolean);
            }
          }

          // Filter out unwanted questions (Submit Feedback, tool names, etc.)
          topicsToProcess = topicsToProcess.filter((topic) => {
            if (!topic.title) return false;

            const title = topic.title.toLowerCase();

            // Skip feedback and submission topics
            if (title.includes("submit") || title.includes("feedback"))
              return false;

            // Skip tool/wizard names (these typically don't have proper question text)
            if (
              title.includes("wizard") ||
              title.includes("generator") ||
              title.includes("designer") ||
              title.includes("analysis") ||
              title.includes("tactics") ||
              title.includes("ideas")
            )
              return false;

            // Skip if topic doesn't have proper field structure
            if (
              !topic.fields ||
              !Array.isArray(topic.fields) ||
              topic.fields.length === 0
            )
              return false;

            // Only keep topics that look like actual survey questions
            return true;
          });

          topicsToProcess.forEach((topic) => {
            if (topic.fields && Array.isArray(topic.fields)) {
              topic.fields.forEach((field) => {
                // Use topic title as question text, not field label
                const questionText = topic.title || field.label || field.name;

                if (!questionAnalysis[questionText]) {
                  questionAnalysis[questionText] = {
                    questionText: questionText,
                    originalFieldName: field.name,
                    totalResponses: 0,
                    responseBreakdown: {},
                    departmentResponses: {},
                    companyResponses: {},
                    allPossibleOptions: [], // Store all options from schema
                  };

                  // Get all possible options from field schema
                  if (field.options && Array.isArray(field.options)) {
                    questionAnalysis[questionText].allPossibleOptions =
                      field.options.map((option) => option.label);

                    // Initialize all options with zero count
                    field.options.forEach((option) => {
                      questionAnalysis[questionText].responseBreakdown[
                        option.label
                      ] = 0;
                    });
                  }
                }
              });
            }
          });
        }
      });

      // Then, process actual responses
      formResponses.forEach((response) => {
        // Find the correct form schema by matching courseId and blockId
        const courseId = response.sourceDetails?.courseId?.toString();
        const blockId = response.formId?.toString(); // This is actually the blockId

        // Find which form_id corresponds to this blockId in the course
        let targetFormSchema = null;

        // Check all forms from this course
        const courseFormIds = courseToFormMapping[courseId] || [];
        for (const formId of courseFormIds) {
          const formSchema = formSchemas[formId];
          if (formSchema) {
            // For now, use any available form schema from this course
            // TODO: We might need to match blockId with contentBlock._id more precisely
            targetFormSchema = formSchema;
            break;
          }
        }

        const formSchema = targetFormSchema;

        response.responses.forEach((answer) => {
          // Find which question this field belongs to
          let questionText = null;
          let fieldSchema = null;

          if (formSchema) {
            // Use translations.en.topics if available, otherwise use main topics
            let topicsToCheck = [];
            if (formSchema.translations?.en?.topics) {
              topicsToCheck = formSchema.translations.en.topics;
            } else if (formSchema.topics) {
              topicsToCheck = formSchema.topics;
            }

            // Apply question filter if provided
            if (questionFilter) {
              if (questionFilter.type === "range") {
                topicsToCheck = topicsToCheck.slice(
                  questionFilter.start,
                  questionFilter.end
                );
              } else if (questionFilter.type === "specific") {
                topicsToCheck = questionFilter.indices
                  .map((index) => topicsToCheck[index])
                  .filter(Boolean);
              }
            }

            // Apply the same filtering as in schema processing
            topicsToCheck = topicsToCheck.filter((topic) => {
              if (!topic.title) return false;

              const title = topic.title.toLowerCase();

              // Skip feedback and submission topics
              if (title.includes("submit") || title.includes("feedback"))
                return false;

              // Skip tool/wizard names
              if (
                title.includes("wizard") ||
                title.includes("generator") ||
                title.includes("designer") ||
                title.includes("analysis") ||
                title.includes("tactics") ||
                title.includes("ideas")
              )
                return false;

              // Skip if topic doesn't have proper field structure
              if (
                !topic.fields ||
                !Array.isArray(topic.fields) ||
                topic.fields.length === 0
              )
                return false;

              return true;
            });

            topicsToCheck.forEach((topic) => {
              if (topic.fields && Array.isArray(topic.fields)) {
                const field = topic.fields.find((f) => f.name === answer.name);
                if (field) {
                  questionText = topic.title || field.label || field.name;
                  fieldSchema = field;
                }
              }
            });
          }

          if (!questionText || !questionAnalysis[questionText]) {
            return; // Skip if question not found
          }

          // Get the response label based on the value
          let responseLabel = "Unknown";

          if (fieldSchema && fieldSchema.options) {
            // Remove "value-" prefix if exists
            let cleanValue = answer.value?.toString() || "";
            if (cleanValue.startsWith("value-")) {
              cleanValue = cleanValue.substring(6);
            }

            // Find option by value or by label match
            const option = fieldSchema.options.find(
              (opt) =>
                opt.value === cleanValue ||
                opt.value === answer.value ||
                opt.label === cleanValue
            );

            if (option) {
              responseLabel = option.label;
            } else {
              // If numeric value, try to find by index
              const numericValue = parseInt(cleanValue);
              if (
                !isNaN(numericValue) &&
                fieldSchema.options[numericValue - 1]
              ) {
                responseLabel = fieldSchema.options[numericValue - 1].label;
              } else {
                responseLabel = cleanValue;
              }
            }
          }

          questionAnalysis[questionText].totalResponses += 1;

          // Count response values
          if (
            !questionAnalysis[questionText].responseBreakdown[responseLabel]
          ) {
            questionAnalysis[questionText].responseBreakdown[responseLabel] = 0;
          }
          questionAnalysis[questionText].responseBreakdown[responseLabel] += 1;

          // Department level analysis
          const userDepartment =
            response.submittedBy?.department ||
            response.submittedBy?.companyDepartment ||
            response.submittedBy?.role ||
            "Unknown Department";

          if (
            !questionAnalysis[questionText].departmentResponses[userDepartment]
          ) {
            questionAnalysis[questionText].departmentResponses[userDepartment] =
              {
                total: 0,
                responses: {},
              };

            // Initialize all options for this department
            questionAnalysis[questionText].allPossibleOptions.forEach(
              (optionLabel) => {
                questionAnalysis[questionText].departmentResponses[
                  userDepartment
                ].responses[optionLabel] = 0;
              }
            );
          }

          questionAnalysis[questionText].departmentResponses[
            userDepartment
          ].total += 1;

          if (
            !questionAnalysis[questionText].departmentResponses[userDepartment]
              .responses[responseLabel]
          ) {
            questionAnalysis[questionText].departmentResponses[
              userDepartment
            ].responses[responseLabel] = 0;
          }
          questionAnalysis[questionText].departmentResponses[
            userDepartment
          ].responses[responseLabel] += 1;
        });
      });

      // Calculate percentages
      Object.keys(questionAnalysis).forEach((questionKey) => {
        const question = questionAnalysis[questionKey];

        // Company-wide percentages
        question.companyPercentages = {};
        Object.keys(question.responseBreakdown).forEach((responseLabel) => {
          question.companyPercentages[responseLabel] = {
            count: question.responseBreakdown[responseLabel],
            percentage:
              question.totalResponses > 0
                ? Math.round(
                    (question.responseBreakdown[responseLabel] /
                      question.totalResponses) *
                      100
                  )
                : 0,
          };
        });

        // Department percentages
        question.departmentPercentages = {};
        Object.keys(question.departmentResponses).forEach((dept) => {
          question.departmentPercentages[dept] = {};
          const deptData = question.departmentResponses[dept];

          Object.keys(deptData.responses).forEach((responseLabel) => {
            question.departmentPercentages[dept][responseLabel] = {
              count: deptData.responses[responseLabel],
              percentage:
                deptData.total > 0
                  ? Math.round(
                      (deptData.responses[responseLabel] / deptData.total) * 100
                    )
                  : 0,
            };
          });
        });
      });

      return {
        courseIds: courseIdArray,
        totalUsers: formResponses.length,
        questions: questionAnalysis,
        responseDate: new Date().toLocaleDateString(),
        dateRange: {
          start: startDate.toLocaleDateString(),
          end: endDate.toLocaleDateString(),
        },
      };
    };

    // Analyze responses for each course with specific question filters
    const courseAnalyses = {};

    // === 1. SHEET SECTIONS ===
    // Introduction Course - Questions 1 and 2 (index 0-1)
    const introSheet1Analysis = await analyzeFormResponsesByCourse(
      introCourseId,
      { type: "range", start: 0, end: 2 }
    );
    if (introSheet1Analysis) {
      courseAnalyses.introductionCourseSheet1 = introSheet1Analysis;
    }

    // Job Specific Courses - Question 1 only (index 0)
    const jobSpecificSheet1Analysis = await analyzeFormResponsesByCourse(
      jobSpecificCourseIds,
      { type: "range", start: 0, end: 1 }
    );
    if (jobSpecificSheet1Analysis) {
      courseAnalyses.jobSpecificCourseSheet1 = jobSpecificSheet1Analysis;
    }

    // === 2. SHEET SECTIONS ===
    // Beginner Journey - AI adoption & productivity survey Questions 1 and 2 (index 0-1)
    // This form should be found in beginner journey courses
    const beginnerJourneyAnalysis = await analyzeFormResponsesByCourse(
      [...[introCourseId], ...aiFoundationsCourseIds], // Search in both intro and AI foundations courses
      { type: "range", start: 0, end: 2 }, // Questions 1 and 2
      "AI adoption" // Filter for "AI adoption & productivity survey" form
    );
    if (beginnerJourneyAnalysis) {
      courseAnalyses.beginnerJourneyProductivitySurvey = beginnerJourneyAnalysis;
    }

    // Expert Journey First Half - Job Specific Questions 2 and 3 (index 1-2)
    const expertJourneyFirstHalfAnalysis = await analyzeFormResponsesByCourse(
      jobSpecificCourseIds,
      { type: "range", start: 1, end: 3 }
    );
    if (expertJourneyFirstHalfAnalysis) {
      courseAnalyses.expertJourneyFirstHalf = expertJourneyFirstHalfAnalysis;
    }

    // Expert Journey Second Half - AI Value Assessment Expert Questions 5 and 6 (index 4-5)
    const expertJourneySecondHalfAnalysis = await analyzeFormResponsesByCourse(
      aiValueAssessmentExpertIds,
      { type: "range", start: 4, end: 6 }
    );
    if (expertJourneySecondHalfAnalysis) {
      courseAnalyses.expertJourneySecondHalf = expertJourneySecondHalfAnalysis;
    }

    // Master Journey First Half - Proactivity Expert Questions 1 and 2 (index 0-1)
    const masterJourneyFirstHalfAnalysis = await analyzeFormResponsesByCourse(
      proactivityExpertId,
      { type: "range", start: 0, end: 2 }
    );
    if (masterJourneyFirstHalfAnalysis) {
      courseAnalyses.masterJourneyFirstHalf = masterJourneyFirstHalfAnalysis;
    }

    // Master Journey Second Half - Master Class Master Questions 5 and 6 (index 4-5)
    const masterJourneySecondHalfAnalysis = await analyzeFormResponsesByCourse(
      masterClassMasterIds,
      { type: "range", start: 4, end: 6 }
    );
    if (masterJourneySecondHalfAnalysis) {
      courseAnalyses.masterJourneySecondHalf = masterJourneySecondHalfAnalysis;
    }

    // Overall statistics
    const totalRespondents = Object.values(courseAnalyses).reduce(
      (total, course) => total + (course.totalUsers || 0),
      0
    );

    const overallStats = {
      totalCompanyUsers: users.length,
      totalRespondents: totalRespondents,
      coursesWithResponses: Object.keys(courseAnalyses).length,
      reportGeneratedAt: new Date().toISOString(),
      dateRange: {
        start: startDate.toLocaleDateString(),
        end: endDate.toLocaleDateString(),
      },
    };

    // Format data for Excel export (similar to the screenshot structure)
    const excelData = {
      title: "AI Adoption Survey / Productivity estimates",
      date: new Date().toLocaleDateString(),
      companyName: company.companyName,
      sections: {},
    };

    // Process each course data for Excel format
    if (courseAnalyses && Object.keys(courseAnalyses).length > 0) {
      Object.keys(courseAnalyses).forEach((courseKey) => {
        const courseData = courseAnalyses[courseKey];
        if (!courseData || !courseData.questions) return;

        // Determine section title based on course key
        let sectionTitle;
        let sheetNumber;

        if (courseKey === "introductionCourseSheet1") {
          sectionTitle = "Introduction Course";
          sheetNumber = 1;
        } else if (courseKey === "jobSpecificCourseSheet1") {
          sectionTitle = "Job Specific Courses";
          sheetNumber = 1;
        } else if (courseKey === "beginnerJourneyProductivitySurvey") {
          sectionTitle = "Beginner journey - AI adoption & productivity survey";
          sheetNumber = 2;
        } else if (courseKey === "expertJourneyFirstHalf") {
          sectionTitle = "Expert journey / first half";
          sheetNumber = 2;
        } else if (courseKey === "expertJourneySecondHalf") {
          sectionTitle = "Expert journey / second half";
          sheetNumber = 2;
        } else if (courseKey === "masterJourneyFirstHalf") {
          sectionTitle = "Master journey / first half";
          sheetNumber = 2;
        } else if (courseKey === "masterJourneySecondHalf") {
          sectionTitle = "Master journey / second half";
          sheetNumber = 2;
        } else {
          sectionTitle = `${courseKey.replace(/([A-Z])/g, " $1").trim()}`;
          sheetNumber = 1;
        }

        excelData.sections[courseKey] = {
          title: sectionTitle,
          sheetNumber: sheetNumber,
          questions: [],
        };

        Object.keys(courseData.questions).forEach((questionKey) => {
          const question = courseData.questions[questionKey];
          if (!question) return;

          const questionData = {
            questionText: question.questionText || questionKey,
            departmentData: {},
            companyData: {},
          };

          // For AI adoption & productivity survey, we need to separate responses by option type
          // a) for our team / department vs b) overall in our company
          if (courseKey === "beginnerJourneyProductivitySurvey") {
            // Initialize separate data structures for team/department vs company responses
            questionData.teamDepartmentData = {};
            questionData.overallCompanyData = {};

            // Process responses and separate by option type
            if (question.companyPercentages) {
              Object.keys(question.companyPercentages).forEach((responseLabel) => {
                const data = question.companyPercentages[responseLabel];
                if (data) {
                  // Check if response is for team/department (option a) or overall company (option b)
                  if (responseLabel.toLowerCase().includes("team") ||
                      responseLabel.toLowerCase().includes("department") ||
                      responseLabel.toLowerCase().includes("a)")) {
                    questionData.teamDepartmentData[responseLabel] = {
                      count: data.count || 0,
                      percentage: (data.percentage || 0) + "%",
                    };
                  } else if (responseLabel.toLowerCase().includes("company") ||
                             responseLabel.toLowerCase().includes("overall") ||
                             responseLabel.toLowerCase().includes("b)")) {
                    questionData.overallCompanyData[responseLabel] = {
                      count: data.count || 0,
                      percentage: (data.percentage || 0) + "%",
                    };
                  } else {
                    // Default to company data if unclear
                    questionData.overallCompanyData[responseLabel] = {
                      count: data.count || 0,
                      percentage: (data.percentage || 0) + "%",
                    };
                  }
                }
              });
            }
          } else {
            // Standard processing for other forms
            // Department data (a) in our team / department)
            if (question.departmentPercentages) {
              Object.keys(question.departmentPercentages).forEach((dept) => {
                if (!questionData.departmentData[dept]) {
                  questionData.departmentData[dept] = [];
                }
                if (question.departmentPercentages[dept]) {
                  Object.keys(question.departmentPercentages[dept]).forEach(
                    (responseLabel) => {
                      const data =
                        question.departmentPercentages[dept][responseLabel];
                      if (data) {
                        questionData.departmentData[dept].push({
                          response: responseLabel,
                          count: data.count || 0,
                          percentage: (data.percentage || 0) + "%",
                        });
                      }
                    }
                  );
                }
              });
            }

            // Company data (b) overall in our company)
            if (question.companyPercentages) {
              Object.keys(question.companyPercentages).forEach(
                (responseLabel) => {
                  const data = question.companyPercentages[responseLabel];
                  if (data) {
                    questionData.companyData[responseLabel] = {
                      count: data.count || 0,
                      percentage: (data.percentage || 0) + "%",
                    };
                  }
                }
              );
            }
          }

          excelData.sections[courseKey].questions.push(questionData);
        });
      });
    }

    // Save report to database
    const report = await ReportModel.create({
      companyName: company.companyName,
      company: companyId,
      reportType: "ai-value",
      downloadLink: "not available",
      reportData: {
        courseAnalyses,
        overallStats,
        excelData,
      },
      createdAt: new Date(),
    });

    return {
      companyName: company.companyName,
      company: companyId,
      reportType: "ai-value",
      status: "success",
      downloadLink: "not available",
      message: "Success",
      data: {
        courseAnalyses,
        overallStats,
        excelData,
        reportId: report._id,
      },
    };
  } catch (err) {
    return {
      reportType: "ai-value",
      status: "error",
      message: `Internal Server Error: ${err.message}`,
      data: null,
    };
  }
};
