const ReportFilesModel = require("../models/ReportFilesModel");
const ReportModel = require("../models/ReportModel");
const UserModel = require("../models/UserModel");
const { httpResponse } = require("../utils/helpers");
const { HTTP_CODES } = require("../utils/enum");
const mongoose = require("mongoose");
const ExcelJS = require("exceljs");
const { BlobServiceClient } = require("@azure/storage-blob");

// Azure Storage configuration
const azureStorageConnectionString =
  process.env.AZURE_STORAGE_CONNECTION_STRING;
const containerName =
  process.env.AZURE_STORAGE_CONTAINER_NAME || "excel-reports";

// File upload helper function for Excel files
const uploadExcelToAzure = async (buffer, fileName) => {
  try {
    if (!azureStorageConnectionString) {
      throw new Error("Azure Storage connection string not configured");
    }

    const blobServiceClient = BlobServiceClient.fromConnectionString(
      azureStorageConnectionString
    );
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Create container if it doesn't exist
    await containerClient.createIfNotExists({
      access: "blob",
    });

    const blobName = `${Date.now()}-${fileName}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    await blockBlobClient.upload(buffer, buffer.length, {
      blobHTTPHeaders: {
        blobContentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      },
    });

    return {
      url: blockBlobClient.url,
      name: blobName,
    };
  } catch (error) {
    console.error("Error uploading Excel file to Azure:", error);
    throw error;
  }
};

// Excel generation functions for different report types
const generateTrainingExcel = async (reportData) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Training_Report");

  // Calculate percentages
  const cohortSize = reportData.cohortSize || 0;
  const loggedInUsers = reportData.loggedInUsers || 0;

  // Get today's date in dd-mm-yyyy format
  const today = new Date();
  const day = String(today.getDate()).padStart(2, "0");
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const year = today.getFullYear();
  const formattedDate = `${day}-${month}-${year}`;

  // Training metrics based on CSV format
  const worksheetData = [
    [`Training progress ${formattedDate}`, "Participants", "%"],
    ["Cohort size", cohortSize, ""],
    ["Logged in", loggedInUsers, "100%"], // Always 100% since report is based on logged in users
    ["Beginner", "", ""],
    [
      "First certificate achieved (Introduction program)",
      reportData.introductionProgramCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.introductionProgramCompleted || 0) / loggedInUsers) *
              100
          )}%`
        : "0%",
    ],
    [
      "Second certificate achieved (Successfully navigate AI risks program)",
      reportData.riskProgramCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.riskProgramCompleted || 0) / loggedInUsers) * 100
          )}%`
        : "0%",
    ],
    [
      "Third certificate achieved (Journey completed)",
      reportData.beginnerJourneyCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.beginnerJourneyCompleted || 0) / loggedInUsers) * 100
          )}%`
        : "0%",
    ],
    ["Expert", "", ""],
    [
      "Fourth certificate achieved (Job-specific use case training)",
      reportData.expertJourneyJobSpecificCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.expertJourneyJobSpecificCompleted || 0) /
              loggedInUsers) *
              100
          )}%`
        : "0%",
    ],
    [
      "Fifth certificate achieved (Journey completed)",
      reportData.expertJourneyCompleted || 0,
      loggedInUsers > 0
        ? `${Math.round(
            ((reportData.expertJourneyCompleted || 0) / loggedInUsers) * 100
          )}%`
        : "0%",
    ],
    ["Master", "", ""],
    [
      "Sixth certificate achieved",
      reportData.masterJourneyCompleted || 0,
      (reportData.masterJourneyCompleted || 0) > 0
        ? `${Math.round(
            ((reportData.masterJourneyCompleted || 0) / loggedInUsers) * 100
          )}%`
        : "N/A",
    ],
  ];

  // Add data to worksheet
  worksheet.addRows(worksheetData);

  // Set column widths
  worksheet.columns = [
    { width: 60 }, // Description column
    { width: 15 }, // Participants column
    { width: 10 }, // Percentage column
  ];

  // Add styling to cells
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell, colNumber) => {
      // Header row styling (row 1) - #366092
      if (rowNumber === 1) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF366092" }, // Dark blue background
        };
        cell.font = { color: { argb: "FFFFFFFF" }, bold: true }; // White bold text
        cell.alignment = { horizontal: "center", vertical: "middle" };
      }
      // Cohort size and Logged in rows (rows 2-3) - #EBF5FB
      else if (rowNumber === 2 || rowNumber === 3) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFEBF5FB" },
        };
        cell.font = { bold: true };
        cell.alignment = {
          horizontal: colNumber === 1 ? "left" : "center",
          vertical: "middle",
        };
      }
      // Group headers (Beginner, Expert, Master) - rows 4, 8, 11 - #7D9CD8
      else if (rowNumber === 4 || rowNumber === 8 || rowNumber === 11) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF7D9CD8" },
        };
        if (colNumber === 1) {
          // Only first column
          cell.font = { bold: true, color: { argb: "FFFFFFFF" } }; // White bold text
          cell.alignment = { horizontal: "left", vertical: "middle" };
        } else {
          cell.alignment = { horizontal: "center", vertical: "middle" };
        }
      }
      // First, Second, Third certificate rows (5-7) - #F2F7FF
      else if (rowNumber === 5 || rowNumber === 6 || rowNumber === 7) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF2F7FF" },
        };
        cell.alignment = {
          horizontal: colNumber === 1 ? "left" : "center",
          vertical: "middle",
        };
      }
      // Fourth and Fifth certificate rows (9-10) - #FEF7F0
      else if (rowNumber === 9 || rowNumber === 10) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFFEF7F0" },
        };
        cell.alignment = {
          horizontal: colNumber === 1 ? "left" : "center",
          vertical: "middle",
        };
      }
      // Sixth certificate row (12) - #FEEAD6
      else if (rowNumber === 12) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFFEEAD6" },
        };
        cell.alignment = {
          horizontal: colNumber === 1 ? "left" : "center",
          vertical: "middle",
        };
      }
    });
  });

  return await workbook.xlsx.writeBuffer();
};

const generateNextgenExcel = async (reportData) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("NextGen_Talent_Radar");

  // Sort users by totalAIScore in descending order for ranking
  const sortedUsers = [...reportData].sort(
    (a, b) => (b.totalAIScore || 0) - (a.totalAIScore || 0)
  );

  // Header row matching CSV format
  const headers = [
    "Ranking",
    "Rating points",
    "Name",
    "Number\nuse cases applied",
    "Number\ndifferent use cases applied",
    "Number ideas\nsubmitted",
    "Number of\ncertificates obtained",
    "Number of\ncreated AI solutions",
    "Completed\ntraining journeys",
  ];

  worksheet.addRow(headers);

  // Add data rows
  sortedUsers.forEach((user, index) => {
    const ranking = index + 1;
    const fullName = `${user.name || ""} ${user.surname || ""}`.trim();

    worksheet.addRow([
      ranking,
      user.totalAIScore || 0,
      fullName,
      user.totalUsecaseUsage || 0,
      user.totalUniqueUsecase || 0,
      user.totalIdeas || 0,
      user.totalCertificates || 0,
      user.totalAISolutions || 0,
      user.totalJourneyCompleted || 0,
    ]);
  });

  // Set column widths for better readability
  worksheet.columns = [
    { width: 10 }, // Ranking
    { width: 15 }, // Rating points
    { width: 20 }, // Name
    { width: 15 }, // Number use cases applied
    { width: 18 }, // Number different use cases applied
    { width: 15 }, // Number ideas submitted
    { width: 18 }, // Number of certificates obtained
    { width: 18 }, // Number of created AI solutions
    { width: 18 }, // Completed training journeys
  ];

  // Column background colors
  const columnColors = [
    "FFFFFFFF", // Ranking - white
    "FFF2F4F4", // Rating points - F2F4F4
    "FFFFFFFF", // Name - white
    "FFEAF2F8", // Number use cases applied - EAF2F8
    "FFD4E6F1", // Number different use cases applied - D4E6F1
    "FFAED6F1", // Number ideas submitted - AED6F1
    "FF7FB3D5", // Number of certificates obtained - 7FB3D5
    "FF85C1E9", // Number of created AI solutions - 85C1E9
    "FF85C1E9", // Completed training journeys - 85C1E9
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.eachCell((cell, colNumber) => {
    // Different styling for first 3 columns vs others
    if (colNumber <= 3) {
      // Ranking, Rating points, Name headers: white background, black text
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFFFFFFF" }, // White background
      };
      cell.font = {
        color: { argb: "FF000000" }, // Black text
        bold: true,
      };
    } else {
      // Other headers: #366092 background, white text
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FF366092" }, // Dark blue background
      };
      cell.font = {
        color: { argb: "FFFFFFFF" }, // White text
        bold: true,
      };
    }

    cell.alignment = {
      horizontal: "center",
      vertical: "middle",
      wrapText: true,
    };
    cell.border = {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    };
  });

  // Style data rows with column-specific colors
  for (let i = 2; i <= worksheet.rowCount; i++) {
    const row = worksheet.getRow(i);
    row.eachCell((cell, colNumber) => {
      // Center align numbers, left align names
      cell.alignment = {
        horizontal: colNumber === 3 ? "left" : "center", // Name column left, others center
        vertical: "middle",
      };
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };

      // Apply column-specific background color
      if (colNumber <= columnColors.length) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: columnColors[colNumber - 1] },
        };
      }
    });
  }

  return await workbook.xlsx.writeBuffer();
};

const generateOnboardingExcel = async (reportData) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Onboarding_Report");

  // Header row
  const headers = ["Name", "Surname", "Email"];

  worksheet.addRow(headers);

  // Add data rows
  reportData.forEach((user) => {
    worksheet.addRow([
      user.name || "Unknown",
      user.surname || "Unknown",
      user.email || "N/A",
    ]);
  });

  // Set column widths for better readability
  worksheet.columns = [
    { width: 20 }, // Name
    { width: 20 }, // Surname
    { width: 35 }, // Email
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.eachCell((cell) => {
    cell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF366092" }, // Dark blue background
    };
    cell.font = {
      color: { argb: "FFFFFFFF" }, // White text
      bold: true,
    };
    cell.alignment = {
      horizontal: "center",
      vertical: "middle",
      wrapText: true,
    };
    cell.border = {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    };
  });

  // Style data rows
  for (let i = 2; i <= worksheet.rowCount; i++) {
    const row = worksheet.getRow(i);
    row.eachCell((cell, colNumber) => {
      // Left align all columns (name, surname, email)
      cell.alignment = {
        horizontal: "left",
        vertical: "middle",
      };
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };

      // Alternating row colors for better readability
      const backgroundColor = i % 2 === 0 ? "FFF8F9FA" : "FFFFFFFF";
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: backgroundColor },
      };
    });
  }

  return await workbook.xlsx.writeBuffer();
};

const generateIdeationExcel = async (reportData) => {
  const workbook = new ExcelJS.Workbook();

  // Add metadata
  workbook.creator = "AIBS Adoption Platform";
  workbook.lastModifiedBy = "AIBS System";
  workbook.created = new Date();
  workbook.modified = new Date();

  // Create single worksheet for ideation data
  const worksheet = workbook.addWorksheet("Ideation Report");

  // Define columns based on main Excel report structure
  worksheet.columns = [
    { header: "Created At", key: "createdAt", width: 15 },
    { header: "User email", key: "userEmail", width: 30 },
    { header: "Title of your idea", key: "titleOfIdea", width: 25 },
    { header: "Describe your idea", key: "describeIdea", width: 30 },
    { header: "Attachement", key: "attachment", width: 12 },
    {
      header: "What is the primary expected business benefit of your idea?",
      key: "primaryBenefit",
      width: 35,
    },
    {
      header: "What is the targeted business area of your idea?",
      key: "targetedBusinessArea",
      width: 30,
    },
    {
      header:
        'Based on the response provided for the benefit inquiry, two distinct questions can be posed: "Estimate the time you would save per week by implementing and applying your use case idea" for respondents who indicated "Better efficiency" or "Cost reduction" as their answer, and a request for clarification for other responses.',
      key: "timeSaved",
      width: 50,
    },
    {
      header:
        "Please describe the benefits of your idea for our business as concrete as possible.",
      key: "concreteBenefits",
      width: 40,
    },
    {
      header:
        "How many of our colleagues in your team / department or overall in our company could benefit from this use case idea?",
      key: "colleaguesBenefit",
      width: 45,
    },
    {
      header:
        "What is the expected impact of your idea in terms of annual business benefit (in EUR, CHF, USD)?",
      key: "annualImpact",
      width: 40,
    },
    {
      header: "What are possible hurdles or risks related to your idea?",
      key: "hurdlesRisks",
      width: 35,
    },
    { header: "Training journey", key: "journeyLevel", width: 15 },
  ];

  // Add data rows
  const submissions = reportData.submissions || [];

  submissions.forEach((submission) => {
    const createdAt = new Date(submission.createdAt);
    const day = String(createdAt.getUTCDate()).padStart(2, "0");
    const month = String(createdAt.getUTCMonth() + 1).padStart(2, "0");
    const year = createdAt.getUTCFullYear();
    const formattedDate = `${day}.${month}.${year}`;

    worksheet.addRow({
      createdAt: formattedDate,
      userEmail: submission.userEmail || "",
      titleOfIdea: submission.titleOfIdea || "",
      describeIdea: submission.describeIdea || "",
      attachment: submission.attachment || "0",
      primaryBenefit: submission.primaryBenefit || "",
      targetedBusinessArea: submission.targetedBusinessArea || "",
      timeSaved: submission.timeSaved || "",
      concreteBenefits: submission.concreteBenefits || "",
      colleaguesBenefit: submission.colleaguesBenefit || "",
      annualImpact: submission.annualImpact || "",
      hurdlesRisks: submission.hurdlesRisks || "",
      journeyLevel: submission.journeyLevel || "",
    });
  });

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.height = 120; // Increase row height for better text wrapping

  // Apply styling to each header cell individually for better control
  headerRow.eachCell((cell, colNumber) => {
    cell.font = {
      bold: true,
      color: { argb: "FFFFFFFF" }, // White text
      size: 11, // Slightly smaller font for better wrapping
    };
    cell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF215E9C" }, // #215E9C background color
    };
    cell.alignment = {
      vertical: "middle", // Center align vertically
      horizontal: "center",
      wrapText: true, // Enable text wrapping
    };
    cell.border = {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    };
  });

  return await workbook.xlsx.writeBuffer();
};

const generateCreatorExcel = (reportData) => {
  const workbook = new ExcelJS.Workbook();

  // Creator data processing
  const worksheetData = [];

  if (reportData.creators) {
    worksheetData.push([
      "Creator Name",
      "Content Type",
      "Views",
      "Engagement",
      "Revenue",
    ]);
    reportData.creators.forEach((creator) => {
      worksheetData.push([
        creator.name || "",
        creator.contentType || "",
        creator.views || "",
        creator.engagement || "",
        creator.revenue || "",
      ]);
    });
  }

  const worksheet = workbook.addWorksheet("Creator Report");
  worksheet.addRows(worksheetData);

  return workbook.xlsx.writeBuffer();
};

const generateApplicationExcel = async (reportData) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Use_Case_Application_Report");

  // Get today's date in dd-mm-yyyy format
  const today = new Date();
  const day = String(today.getDate()).padStart(2, "0");
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const year = today.getFullYear();
  const formattedDate = `${day}-${month}-${year}`;

  // Extract data
  const totalActiveUser = reportData.totalActiveUser || 0;
  const totalUsecaseUsage = reportData.totalUsecaseUsage || 0;
  const totalUniqueUsecase = reportData.totalUniqueUsecase || 0;

  // Calculate averages
  const avgUsecasePerUser =
    totalActiveUser > 0
      ? (totalUsecaseUsage / totalActiveUser).toFixed(1)
      : "0";
  const avgUniqueUsecasePerUser =
    totalActiveUser > 0
      ? (totalUniqueUsecase / totalActiveUser).toFixed(1)
      : "0";

  // Create data rows matching CSV format
  const reportRows = [
    [`Use case applications (${formattedDate})`],
    [`Total of active users`, totalActiveUser],
    [`Total number of use cases applied`, totalUsecaseUsage],
    [
      `Total number of different types of use cases applied`,
      totalUniqueUsecase,
    ],
    [`Average number of use cases applied per user`, avgUsecasePerUser],
    [
      `Average number of different types of use cases applied per user`,
      avgUniqueUsecasePerUser,
    ],
  ];

  // Add rows to worksheet
  reportRows.forEach((row) => {
    worksheet.addRow(row);
  });

  // Set column widths
  worksheet.columns = [
    { width: 60 }, // Description column
    { width: 20 }, // Value column
  ];

  // Row-specific background colors for descriptions
  const rowColors = {
    2: "FFB4EAF5", // Total of active users
    3: "FF93CFDC", // Total number of use cases applied
    4: "FF75B7C5", // Total number of different types of use cases applied
    5: "FF5299A8", // Average number of use cases applied per user
    6: "FF4B7F8A", // Average number of different types of use cases applied per user
  };

  // Style the rows
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell, colNumber) => {
      if (rowNumber === 1) {
        // Header row styling
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF1D6799" }, // Header background color
        };
        cell.font = {
          color: { argb: "FFFFFFFF" }, // White text
          bold: false,
          size: 12,
        };
        cell.alignment = {
          horizontal: "left",
          vertical: "middle",
        };
      } else {
        // Data rows styling
        if (colNumber === 1) {
          // Description column - specific color for each row
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: rowColors[rowNumber] || "FFFFFFFF" },
          };
          cell.font = { bold: false };
          cell.alignment = {
            horizontal: "left",
            vertical: "middle",
          };
        } else {
          // Value column - consistent color for all numbers
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFFFDDC3" }, // Numbers column background color
          };
          cell.alignment = {
            horizontal: "right",
            vertical: "middle",
          };
          cell.font = { bold: true };
        }
      }

      // No borders - removed border styling
    });
  });

  // Merge the header row across both columns
  worksheet.mergeCells("A1:B1");

  return await workbook.xlsx.writeBuffer();
};

const generateAiValueExcel = async (reportData) => {
  const workbook = new ExcelJS.Workbook();

  // Helper function to update column width based on content
  const updateColumnWidth = (colIndex, content, columnWidths) => {
    if (content && typeof content === "string") {
      const contentLength = content.length;
      if (contentLength > columnWidths[colIndex]) {
        columnWidths[colIndex] = contentLength;
      }
    }
  };

  // Helper function to set cell with wrap text for long content
  const setCellWithWrap = (cell, content, minRowHeight = 20) => {
    cell.value = content;
    if (content && typeof content === "string" && content.length > 50) {
      cell.alignment = {
        ...cell.alignment,
        wrapText: true,
        vertical: "top",
      };
      // Set minimum row height for wrapped text
      const row = cell.row;
      if (!row.height || row.height < minRowHeight) {
        row.height = Math.max(minRowHeight, Math.min(content.length / 2, 100));
      }
    }
  };

  // Helper function to create worksheet content
  const createWorksheetContent = (
    worksheet,
    filterConfig,
    worksheetTitle,
    columnWidths
  ) => {
    let currentRow = 1;
    const sectionHeaderRows = []; // Track section header rows to exclude from borders

    // Main title - merge based on worksheet type
    let titleMergeRange;
    if (worksheetTitle === "Personal productivity") {
      titleMergeRange = `A${currentRow}:C${currentRow}`;
    } else {
      titleMergeRange = `A${currentRow}:F${currentRow}`;
    }
    worksheet.mergeCells(titleMergeRange);

    const titleCell = worksheet.getCell(`A${currentRow}`);
    const titleText =
      worksheetTitle || "AI Adoption Survey / Productivity estimates";
    setCellWithWrap(titleCell, titleText);
    titleCell.font = { bold: true, size: 16, color: { argb: "00000000" } };
    titleCell.alignment = {
      ...titleCell.alignment,
      horizontal: "left",
      vertical: "middle",
    };
    titleCell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFB4C7E7" },
    };
    updateColumnWidth(0, titleText, columnWidths);
    currentRow += 2;

    // Date
    worksheet.getCell(`A${currentRow}`).value = "Date";
    worksheet.getCell(`A${currentRow}`).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "DEE9F8" },
    };
    worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 11 };
    worksheet.getCell(`A${currentRow}`).alignment = {
      horizontal: "left",
      vertical: "middle",
    };
    const dateValue =
      reportData.excelData?.date || new Date().toLocaleDateString();
    worksheet.getCell(`B${currentRow}`).value = dateValue;
    updateColumnWidth(0, "Date", columnWidths);
    updateColumnWidth(1, dateValue, columnWidths);
    currentRow += 2;

    // Process each course section with explicit ordering for 2nd sheet
    if (reportData.excelData && reportData.excelData.sections) {
      let courseKeysToProcess;
      if (worksheetTitle === "Personal productivity") {
        // Explicit order for 2nd sheet: Beginner -> Expert -> Master journey sections
        courseKeysToProcess = [
          "beginnerJourneyProductivitySurvey",
          "expertJourneyFirstHalf",
          "expertJourneySecondHalf",
          "masterJourneyFirstHalf",
          "masterJourneySecondHalf",
        ];
      } else {
        // For 1st sheet: Introduction + Job Specific courses
        courseKeysToProcess = [
          "introductionCourseSheet1",
          "jobSpecificCourseSheet1",
        ];
      }

      courseKeysToProcess.forEach((courseKey) => {
        if (!reportData.excelData.sections[courseKey]) return; // Skip if course doesn't exist
        const courseData = reportData.excelData.sections[courseKey];

        // Define course-level flags for single response detection
        const isBeginnerJourneyProductivitySurvey = courseKey === "beginnerJourneyProductivitySurvey";

        // Add section headers for both sheets
        const shouldAddSectionHeader =
          worksheetTitle === "Personal productivity" ||
          worksheetTitle === "AI Adoption Survey / Productivity estimates";

        if (shouldAddSectionHeader) {
          // Different merge ranges for different sheets
          const headerMergeRange =
            worksheetTitle === "Personal productivity"
              ? `A${currentRow}:C${currentRow}`
              : `A${currentRow}:F${currentRow}`;

          worksheet.mergeCells(headerMergeRange);
          const sectionHeader = worksheet.getCell(`A${currentRow}`);

          let sectionHeaderText;
          // 2nd sheet headers
          if (courseKey === "beginnerJourneyProductivitySurvey") {
            sectionHeaderText = "Beginner journey - AI adoption & productivity survey";
          } else if (courseKey === "expertJourneyFirstHalf") {
            sectionHeaderText = "Expert journey / first half";
          } else if (courseKey === "expertJourneySecondHalf") {
            sectionHeaderText = "Expert journey / second half";
          } else if (courseKey === "masterJourneyFirstHalf") {
            sectionHeaderText = "Master journey / first half";
          } else if (courseKey === "masterJourneySecondHalf") {
            sectionHeaderText = "Master journey / second half";
          }
          // 1st sheet headers
          else if (courseKey === "introductionCourseSheet1") {
            sectionHeaderText = "Beginner Journey";
          } else if (courseKey === "jobSpecificCourseSheet1") {
            sectionHeaderText = "Expert Journey";
          }

          setCellWithWrap(sectionHeader, sectionHeaderText);
          sectionHeader.font = {
            bold: true,
            size: 14,
            color: { argb: "00000000" },
          };
          sectionHeader.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFE6E6FA" },
          };
          sectionHeader.alignment = {
            ...sectionHeader.alignment,
            horizontal: "left",
            vertical: "middle",
          };
          updateColumnWidth(0, sectionHeaderText, columnWidths);
          sectionHeaderRows.push(currentRow); // Track this row as section header
          currentRow += 1;
        }

        // Process questions based on filter
        let filteredQuestions = courseData.questions;

        // If filterConfig is provided, use course-specific + question-specific filtering
        if (
          filterConfig &&
          typeof filterConfig === "object" &&
          !Array.isArray(filterConfig)
        ) {
          if (filterConfig[courseKey]) {
            filteredQuestions = courseData.questions.filter((_, index) =>
              filterConfig[courseKey].includes(index)
            );
          } else {
            filteredQuestions = []; // If course not in filter config, exclude all questions
          }
        }
        // If filterConfig is simple array (backward compatibility), use old logic
        else if (Array.isArray(filterConfig)) {
          filteredQuestions = courseData.questions.filter((_, index) =>
            filterConfig.includes(index)
          );
        }

        filteredQuestions.forEach((question, questionIndex) => {
          // Question number and text - merge based on question type
          // For 2nd sheet, ALL questions should be single response (A:C merge)
          const isQuestionSingleResponse =
            worksheetTitle === "Personal productivity" ||
            isBeginnerJourneyProductivitySurvey ||
            !question.departmentData ||
            Object.keys(question.departmentData).length === 0;

          let questionMergeRange;
          if (
            worksheetTitle === "Personal productivity" &&
            isQuestionSingleResponse
          ) {
            questionMergeRange = `A${currentRow}:C${currentRow}`;
          } else {
            questionMergeRange = `A${currentRow}:F${currentRow}`;
          }
          worksheet.mergeCells(questionMergeRange);

          const questionCell = worksheet.getCell(`A${currentRow}`);
          // Remove existing numbering from question text and add section-based numbering
          const cleanQuestionText = question.questionText.replace(
            /^\d+\.\s*/,
            ""
          );
          setCellWithWrap(
            questionCell,
            `${questionIndex + 1}. ${cleanQuestionText}`,
            40
          );
          questionCell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "DEE9F8" },
          };
          questionCell.font = { bold: true, size: 11 };
          questionCell.alignment = {
            ...questionCell.alignment,
            horizontal: "left",
            vertical: "top",
          };
          updateColumnWidth(0, question.questionText, columnWidths);
          currentRow += 1;

          // Check if this question has department/company distinction
          const hasDepartmentData =
            question.departmentData &&
            Object.keys(question.departmentData).length > 0 &&
            Object.keys(question.departmentData).some(
              (dept) =>
                question.departmentData[dept] &&
                Array.isArray(question.departmentData[dept]) &&
                question.departmentData[dept].length > 0
            );

          // Special handling for questions without department/company distinction
          const isBeginnerProductivitySurvey = isBeginnerJourneyProductivitySurvey;

          // Expert Journey personal time and job satisfaction questions should be single response
          const isExpertPersonalQuestion =
            courseKey === "expertJourneyFirstHalf" &&
            (question.questionText.toLowerCase().includes("personal time") ||
              question.questionText.toLowerCase().includes("job satisfaction"));

          // ALL questions in 2nd sheet (Personal productivity) should be single response (limited to column C)
          const isSecondSheet = worksheetTitle === "Personal productivity";

          const isSingleResponseQuestion =
            isSecondSheet ||
            isBeginnerProductivitySurvey ||
            isExpertPersonalQuestion ||
            !hasDepartmentData;

          let headers;
          if (isSingleResponseQuestion) {
            // Simple headers for single response questions
            // For 2nd sheet, only use 3 columns
            if (worksheetTitle === "Personal productivity") {
              headers = ["Options", "Count", "%"];
            } else {
              headers = [
                "Options",
                "Count",
                "%",
                "", // Empty columns for 1st sheet compatibility
                "",
                "",
              ];
            }
          } else {
            // Standard headers for questions with department/company distinction
            headers = [
              "a) in our team / department",
              "count",
              "%",
              "b) overall in our company",
              "count",
              "%",
            ];
          }

          // For 2nd sheet, only write headers for first 3 columns
          const headersToWrite =
            worksheetTitle === "Personal productivity"
              ? headers.slice(0, 3)
              : headers;

          headersToWrite.forEach((header, index) => {
            const cell = worksheet.getCell(currentRow, index + 1);
            setCellWithWrap(cell, header);
            cell.font = { bold: true, size: 10 };
            // Center align count and % headers (columns B, C, E, F)
            if (index === 1 || index === 2 || index === 4 || index === 5) {
              cell.alignment = {
                ...cell.alignment,
                horizontal: "center",
                vertical: "middle",
              };
            } else {
              cell.alignment = {
                ...cell.alignment,
                horizontal: "left",
                vertical: "middle",
              };
            }
            updateColumnWidth(index, header, columnWidths);
          });

          currentRow += 1;

          // Handle data based on question type
          let departmentResponses = [];
          let companyResponses = question.companyData
            ? Object.keys(question.companyData)
            : [];

          if (isSingleResponseQuestion) {
            // For single response questions, only show company data
            companyResponses.forEach((responseKey) => {
              const companyData = question.companyData[responseKey];
              if (!companyData) return; // Skip if no data

              const countText = companyData.count
                ? companyData.count.toString()
                : "0";
              const percentageText = companyData.percentage || "0%";

              // Response text in column A
              const responseCell = worksheet.getCell(`A${currentRow}`);
              setCellWithWrap(responseCell, responseKey, 25);

              // Count in column B
              worksheet.getCell(`B${currentRow}`).value =
                companyData.count || 0;
              worksheet.getCell(`B${currentRow}`).alignment = {
                horizontal: "center",
                vertical: "top",
              };

              // Percentage in column C
              worksheet.getCell(`C${currentRow}`).value = percentageText;
              worksheet.getCell(`C${currentRow}`).alignment = {
                horizontal: "center",
                vertical: "top",
              };

              updateColumnWidth(0, responseKey, columnWidths);
              updateColumnWidth(1, countText, columnWidths);
              updateColumnWidth(2, percentageText, columnWidths);

              // Style data row - only first 3 columns for single response questions
              for (let col = 1; col <= 3; col++) {
                const cell = worksheet.getCell(currentRow, col);
                if (col === 2 || col === 3) {
                  cell.alignment = { horizontal: "center", vertical: "top" };
                } else {
                  cell.alignment = { horizontal: "left", vertical: "top" };
                }

                // Alternating row colors
                if (currentRow % 2 === 0) {
                  cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "DEE9F8" },
                  };
                }
              }
              currentRow += 1;
            });
          } else {
            // Standard processing for questions with department/company distinction
            departmentResponses = question.departmentData
              ? Object.keys(question.departmentData)
              : [];

            // Find maximum rows needed
            const maxDeptResponses = Math.max(
              0,
              ...departmentResponses.map((dept) =>
                question.departmentData && question.departmentData[dept]
                  ? question.departmentData[dept].length
                  : 0
              )
            );
            const maxRows = Math.max(maxDeptResponses, companyResponses.length);

            // Add response data rows
            for (let i = 0; i < maxRows; i++) {
              // Department data (first department for simplicity)
              if (
                departmentResponses.length > 0 &&
                question.departmentData &&
                question.departmentData[departmentResponses[0]]
              ) {
                const deptData =
                  question.departmentData[departmentResponses[0]];
                if (i < deptData.length) {
                  const responseText = deptData[i].response || "";
                  const countText = deptData[i].count
                    ? deptData[i].count.toString()
                    : "0";
                  const percentageText = deptData[i].percentage || "0%";

                  const responseCell = worksheet.getCell(`A${currentRow}`);
                  setCellWithWrap(responseCell, responseText, 25);
                  worksheet.getCell(`B${currentRow}`).value =
                    deptData[i].count || 0;
                  worksheet.getCell(`B${currentRow}`).alignment = {
                    horizontal: "center",
                    vertical: "top",
                  };
                  worksheet.getCell(`C${currentRow}`).value = percentageText;
                  worksheet.getCell(`C${currentRow}`).alignment = {
                    horizontal: "center",
                    vertical: "top",
                  };

                  updateColumnWidth(0, responseText, columnWidths);
                  updateColumnWidth(1, countText, columnWidths);
                  updateColumnWidth(2, percentageText, columnWidths);
                }
              }

              // Company data
              if (i < companyResponses.length) {
                const responseKey = companyResponses[i];
                const companyData = question.companyData[responseKey];
                if (!companyData) continue; // Skip if no data

                const countText = companyData.count
                  ? companyData.count.toString()
                  : "0";
                const percentageText = companyData.percentage || "0%";

                const companyResponseCell = worksheet.getCell(`D${currentRow}`);
                setCellWithWrap(companyResponseCell, responseKey, 25);
                worksheet.getCell(`E${currentRow}`).value =
                  companyData.count || 0;
                worksheet.getCell(`E${currentRow}`).alignment = {
                  horizontal: "center",
                  vertical: "top",
                };
                worksheet.getCell(`F${currentRow}`).value = percentageText;
                worksheet.getCell(`F${currentRow}`).alignment = {
                  horizontal: "center",
                  vertical: "top",
                };

                updateColumnWidth(3, responseKey, columnWidths);
                updateColumnWidth(4, countText, columnWidths);
                updateColumnWidth(5, percentageText, columnWidths);
              }

              // Style data rows
              for (let col = 1; col <= 6; col++) {
                const cell = worksheet.getCell(currentRow, col);
                if (!cell.alignment) {
                  // Center align count and % columns (B, C, E, F)
                  if (col === 2 || col === 3 || col === 5 || col === 6) {
                    cell.alignment = { horizontal: "center", vertical: "top" };
                  } else {
                    cell.alignment = { horizontal: "left", vertical: "top" };
                  }
                }

                // Alternating row colors
                if (currentRow % 2 === 0) {
                  cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "DEE9F8" },
                  };
                }
              }
              currentRow += 1;
            }
          }

          // Add Average row for productivity gains questions only
          const isProductivityQuestion =
            question.questionText &&
            (question.questionText.toLowerCase().includes("productivity") ||
              question.questionText.toLowerCase().includes("gains"));

          // Personal productivity gains average for first questions in specific sections in 2nd sheet
          const isPersonalProductivityFirstQuestion =
            worksheetTitle === "Personal productivity" &&
            questionIndex === 0 && // İlk soru
            (courseKey === "beginnerJourneyProductivitySurvey" ||
              courseKey === "expertJourneyFirstHalf" ||
              courseKey === "expertJourneySecondHalf" ||
              courseKey === "masterJourneyFirstHalf" ||
              courseKey === "masterJourneySecondHalf");

          const shouldShowPersonalProductivityAverage =
            isPersonalProductivityFirstQuestion;

          // Don't show old average system if personal productivity average will be shown
          const shouldShowAverage =
            isProductivityQuestion && !shouldShowPersonalProductivityAverage;

          // Personal productivity gains average calculation (define outside to be accessible)
          const calculatePersonalProductivityAverage = (responses) => {
            const personalProductivityWeights = {
              "0%": 0,
              // Format 1: "X % (weekly savings of approx. Y hour(s))"
              "2.5 % (weekly savings of approx. 1 hour)": 2.5,
              "5 % (weekly savings of approx. 2 hours)": 5,
              "7.5 % (weekly savings of approx. 3 hours)": 7.5,
              "10 % (weekly savings of approx. 4 hours)": 10,
              "15 % (weekly savings of approx. 6 hours)": 15,
              "20 % (weekly savings of approx. 8 hours)": 20,
              "25 % (weekly savings of approx. 10 hours)": 25,
              "30 % (weekly savings of approx 12 hours)": 30,
              "40 % (weekly savings of approx. 16 hours)": 40,
              "50 % (weekly savings of approx. 20 hours)": 50,
              "60 % (weekly savings of approx. 24 hours)": 60,
              "70 % (weekly savings of approx. 28 hours)": 70,
              "80 % (weekly savings of approx. 32 hours)": 80,
              "90 % (weekly savings of approx. 36 hours)": 90,
              "100 % (weekly savings of approx. 40 hours)": 100,
              // Format 2: "Weekly savings of approx. Y hour(s)"
              "Weekly savings of approx. 1 hour": 2.5,
              "Weekly savings of approx. 2 hours": 5,
              "Weekly savings of approx. 3 hours": 7.5,
              "Weekly savings of approx. 4 hours": 10,
              "Weekly savings of approx. 6 hours": 15,
              "Weekly savings of approx. 8 hours": 20,
              "Weekly savings of approx. 10 hours": 25,
              "Weekly savings of approx. 12 hours": 30,
              "Weekly savings of approx. 16 hours": 40,
              "Weekly savings of approx. 20 hours": 50,
              "Weekly savings of approx. 24 hours": 60,
              "Weekly savings of approx. 28 hours": 70,
              "Weekly savings of approx. 32 hours": 80,
              "Weekly savings of approx. 36 hours": 90,
              "Weekly savings of approx. 40 hours": 100,
            };

            let totalWeightedValue = 0;
            let totalResponses = 0;

            Object.keys(responses).forEach((responseLabel) => {
              const count = parseInt(responses[responseLabel].count) || 0;
              const weight = personalProductivityWeights[responseLabel];

              if (weight !== undefined && count > 0) {
                totalWeightedValue += weight * count;
                totalResponses += count;
              }
            });

            if (totalResponses === 0) return null;
            // Excel formülüne uygun: weighted_sum/total_responses (yüzde olarak)
            return Math.round((totalWeightedValue / totalResponses) * 10) / 10;
          };

          if (shouldShowAverage) {
            // Calculate weighted average for this specific question
            const calculateQuestionAverage = (responses) => {
              const productivityWeights = {
                0: 0,
                "Up to 5 % (weekly savings of approx. 2 hours)": 2.5,
                "5-10 % (weekly savings of approx. 2-4 hours)": 7.5,
                "10-25 % (weekly savings of approx. 5-10 hours)": 17.5,
                "25-50 % (weekly savings of approx. 10-20 hours)": 37.5,
                "More than 50 % (weekly savings of min. 20 hours)": 75,
                "I don't know / I am not sure": 0,
                "I don't know/I am not sure": 0,
              };

              let totalWeightedValue = 0;
              let totalResponses = 0;

              Object.keys(responses).forEach((responseLabel) => {
                const count = parseInt(responses[responseLabel].count) || 0;
                const weight = productivityWeights[responseLabel];

                if (weight !== undefined && count > 0) {
                  totalWeightedValue += weight * count;
                  totalResponses += count;
                }
              });

              if (totalResponses === 0) return null;
              // Excel formülüne uygun hesaplama: weighted_sum/total_responses (yüzde olarak)
              return (
                Math.round((totalWeightedValue / totalResponses) * 10) / 10
              );
            };

            // For single response questions, handle average differently
            if (isSingleResponseQuestion) {
              // Simple average row spanning first 3 columns
              worksheet.getCell(`A${currentRow}`).value = "Average";
              worksheet.getCell(`A${currentRow}`).font = {
                bold: true,
                size: 10,
              };
              worksheet.getCell(`A${currentRow}`).fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "FFE6E6FA" },
              };

              // Calculate and merge B:C for average
              worksheet.mergeCells(`B${currentRow}:C${currentRow}`);
              const companyAverage = question.companyData
                ? calculateQuestionAverage(question.companyData)
                : null;
              if (companyAverage !== null) {
                worksheet.getCell(`B${currentRow}`).value =
                  companyAverage + "%";
              }
              worksheet.getCell(`B${currentRow}`).alignment = {
                horizontal: "center",
                vertical: "top",
              };
              worksheet.getCell(`B${currentRow}`).font = {
                bold: true,
                size: 10,
              };
              worksheet.getCell(`B${currentRow}`).fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "FFE6E6FA" },
              };

              // Add borders to average row (first 3 columns only)
              for (let col = 1; col <= 3; col++) {
                const cell = worksheet.getCell(currentRow, col);
                cell.border = {
                  top: { style: "thin" },
                  left: { style: "thin" },
                  bottom: { style: "thin" },
                  right: { style: "thin" },
                };
              }
            } else {
              // Standard average row for department/company questions
              worksheet.getCell(`A${currentRow}`).value = "Average";
              worksheet.getCell(`A${currentRow}`).font = {
                bold: true,
                size: 10,
              };
              worksheet.getCell(`A${currentRow}`).fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "FFE6E6FA" }, // Light purple for average
              };

              // Calculate department average (first department)
              let deptAverage = null;
              if (
                departmentResponses.length > 0 &&
                question.departmentData &&
                question.departmentData[departmentResponses[0]]
              ) {
                const deptResponses = {};
                question.departmentData[departmentResponses[0]].forEach(
                  (item) => {
                    if (item && item.response) {
                      deptResponses[item.response] = { count: item.count || 0 };
                    }
                  }
                );
                deptAverage = calculateQuestionAverage(deptResponses);
              }

              // Merge B and C cells for department average
              worksheet.mergeCells(`B${currentRow}:C${currentRow}`);
              if (deptAverage !== null) {
                worksheet.getCell(`B${currentRow}`).value = deptAverage + "%";
              }
              worksheet.getCell(`B${currentRow}`).alignment = {
                horizontal: "center",
                vertical: "top",
              };
              worksheet.getCell(`B${currentRow}`).font = {
                bold: true,
                size: 10,
              };
              worksheet.getCell(`B${currentRow}`).fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "FFE6E6FA" },
              };

              // Company average
              worksheet.getCell(`D${currentRow}`).value = "Average";
              worksheet.getCell(`D${currentRow}`).font = {
                bold: true,
                size: 10,
              };
              worksheet.getCell(`D${currentRow}`).fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "FFE6E6FA" },
              };

              // Merge E and F cells for company average
              worksheet.mergeCells(`E${currentRow}:F${currentRow}`);

              // Calculate company average
              const companyAverage = question.companyData
                ? calculateQuestionAverage(question.companyData)
                : null;
              if (companyAverage !== null) {
                worksheet.getCell(`E${currentRow}`).value =
                  companyAverage + "%";
              }
              worksheet.getCell(`E${currentRow}`).alignment = {
                horizontal: "center",
                vertical: "top",
              };
              worksheet.getCell(`E${currentRow}`).font = {
                bold: true,
                size: 10,
              };
              worksheet.getCell(`E${currentRow}`).fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "FFE6E6FA" },
              };

              // Add borders to average row
              for (let col = 1; col <= 6; col++) {
                const cell = worksheet.getCell(currentRow, col);
                cell.border = {
                  top: { style: "thin" },
                  left: { style: "thin" },
                  bottom: { style: "thin" },
                  right: { style: "thin" },
                };
              }
            }

            currentRow += 1;
          }

          // Personal productivity gains average (for first questions in specific 2nd sheet sections)
          if (shouldShowPersonalProductivityAverage) {
            // Simple average row spanning first 3 columns
            worksheet.getCell(`A${currentRow}`).value = "Average";
            worksheet.getCell(`A${currentRow}`).font = {
              bold: true,
              size: 10,
            };
            worksheet.getCell(`A${currentRow}`).fill = {
              type: "pattern",
              pattern: "solid",
              fgColor: { argb: "FFE6E6FA" },
            };

            // Calculate and merge B:C for average
            worksheet.mergeCells(`B${currentRow}:C${currentRow}`);
            const personalAverage = question.companyData
              ? calculatePersonalProductivityAverage(question.companyData)
              : null;
            if (personalAverage !== null) {
              worksheet.getCell(`B${currentRow}`).value = personalAverage + "%";
            }
            worksheet.getCell(`B${currentRow}`).alignment = {
              horizontal: "center",
              vertical: "top",
            };
            worksheet.getCell(`B${currentRow}`).font = {
              bold: true,
              size: 10,
            };
            worksheet.getCell(`B${currentRow}`).fill = {
              type: "pattern",
              pattern: "solid",
              fgColor: { argb: "FFE6E6FA" },
            };

            // Add borders to average row (first 3 columns only)
            for (let col = 1; col <= 3; col++) {
              const cell = worksheet.getCell(currentRow, col);
              cell.border = {
                top: { style: "thin" },
                left: { style: "thin" },
                bottom: { style: "thin" },
                right: { style: "thin" },
              };
            }

            currentRow += 1;
          }

          // Add spacing between questions
          currentRow += 1;
        });
      });
    }

    // Set column widths based on content with minimum and maximum limits
    // For "Personal productivity" sheet, only set widths for first 3 columns
    const columnsToProcess = worksheetTitle === "Personal productivity" ? 3 : 6;

    worksheet.columns = columnWidths
      .slice(0, columnsToProcess)
      .map((width, index) => {
        // Add some padding and set reasonable min/max limits
        const adjustedWidth = Math.max(10, Math.min(width + 5, 80));
        return { width: adjustedWidth };
      });

    // Add borders to all cells with data (except section headers)
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber <= currentRow && !sectionHeaderRows.includes(rowNumber)) {
        // For "Personal productivity" sheet, only add borders to first 3 columns
        const maxColumn = worksheetTitle === "Personal productivity" ? 3 : 6;

        for (let colNumber = 1; colNumber <= maxColumn; colNumber++) {
          const cell = worksheet.getCell(rowNumber, colNumber);
          if (
            cell.value !== undefined &&
            cell.value !== null &&
            cell.value !== ""
          ) {
            cell.border = {
              top: { style: "thin" },
              left: { style: "thin" },
              bottom: { style: "thin" },
              right: { style: "thin" },
            };
          }
        }
      }
    });

    return currentRow;
  };

  // Create first worksheet - Introduction Course + Job Specific Courses
  const worksheet1 = workbook.addWorksheet("Productivity estimates");
  const columnWidths1 = [0, 0, 0, 0, 0, 0];
  const filter1 = null; // No filter needed - sections already contain correct questions
  createWorksheetContent(
    worksheet1,
    filter1,
    "AI Adoption Survey / Productivity estimates",
    columnWidths1
  );

  // Create second worksheet - Beginner Journey + Expert Journey + Master Journey
  const worksheet2 = workbook.addWorksheet("Personal productivity");
  const columnWidths2 = [0, 0, 0]; // Only 3 columns for 2nd sheet
  const filter2 = null; // No filter needed - sections already contain correct questions

  createWorksheetContent(
    worksheet2,
    filter2,
    "Personal productivity",
    columnWidths2
  );

  return await workbook.xlsx.writeBuffer();
};

const generateMsTrainingExcel = (reportData) => {
  const workbook = new ExcelJS.Workbook();

  // MS Training data processing
  const worksheetData = [];

  if (reportData.trainees) {
    worksheetData.push([
      "Trainee Name",
      "Course",
      "Progress",
      "Certification",
      "Date",
    ]);
    reportData.trainees.forEach((trainee) => {
      worksheetData.push([
        trainee.name || "",
        trainee.course || "",
        trainee.progress || "",
        trainee.certification || "",
        trainee.date || "",
      ]);
    });
  }

  const worksheet = workbook.addWorksheet("MS Training Report");
  worksheet.addRows(worksheetData);

  return workbook.xlsx.writeBuffer();
};

// Excel generation dispatcher
const generateExcelByType = async (reportType, reportData) => {
  switch (reportType) {
    case "training":
      return await generateTrainingExcel(reportData);
    case "nextgen":
      return await generateNextgenExcel(reportData);
    case "ideation":
      return await generateIdeationExcel(reportData);
    case "creator":
      return await generateCreatorExcel(reportData);
    case "application":
      return await generateApplicationExcel(reportData);
    case "ai-value":
      return await generateAiValueExcel(reportData);
    case "ms-training":
      return await generateMsTrainingExcel(reportData);
    case "onboarding":
      return await generateOnboardingExcel(reportData);
    default:
      throw new Error(`Unsupported report type: ${reportType}`);
  }
};

// Background job processor (simple queue system)
const processExcelGeneration = async (reportFileId) => {
  try {
    const reportFile = await ReportFilesModel.findById(reportFileId);
    if (!reportFile) {
      throw new Error("Report file record not found");
    }

    // Update status
    await ReportFilesModel.findByIdAndUpdate(reportFileId, {
      status: "processing",
    });

    // Fetch report data
    const report = await ReportModel.findById(reportFile.reportId);
    if (!report) {
      throw new Error("Report not found");
    }

    // Generate Excel file
    const excelBuffer = await generateExcelByType(
      report.reportType,
      report.reportData
    );

    // Create file name
    const fileName = `${report.companyName}_${report.reportType}_report_${
      new Date().toISOString().split("T")[0]
    }.xlsx`;

    // Upload to Azure
    const uploadResult = await uploadExcelToAzure(excelBuffer, fileName);

    // Update as successful
    await ReportFilesModel.findByIdAndUpdate(reportFileId, {
      status: "ready",
      fileUrl: uploadResult.url,
      fileName: uploadResult.name,
      completedAt: new Date(),
    });
  } catch (error) {
    console.error("Error processing Excel generation:", error);

    // Update error status
    await ReportFilesModel.findByIdAndUpdate(reportFileId, {
      status: "failed",
      error: error.message,
    });
  }
};

// Request Excel file generation
exports.generateExcel = async (req, res) => {
  try {
    const { reportId } = req.body;

    if (!reportId) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Report ID is required"
      );
    }

    // ObjectId validation
    if (!mongoose.Types.ObjectId.isValid(reportId)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid report ID format"
      );
    }

    // User validation
    if (!req.user || !req.user.email) {
      return httpResponse(
        res,
        HTTP_CODES.UNAUTHORIZED,
        "error",
        "User not authenticated"
      );
    }

    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    // Check if report exists
    const report = await ReportModel.findById(reportId);
    if (!report) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Report not found"
      );
    }

    // Check if Excel file has already been generated for this report
    const existingReportFile = await ReportFilesModel.findOne({
      reportId: reportId,
      requestedBy: user._id,
      status: { $in: ["pending", "processing", "ready"] },
    });

    if (existingReportFile) {
      return httpResponse(
        res,
        HTTP_CODES.OK,
        "success",
        "Excel file request already exists",
        existingReportFile
      );
    }

    // Create new report file record
    const reportFile = new ReportFilesModel({
      reportId: reportId,
      requestedBy: user._id,
      status: "pending",
    });

    await reportFile.save();

    // Start background process (in production, use Redis queue)
    setTimeout(() => {
      processExcelGeneration(reportFile._id);
    }, 1000);

    return httpResponse(
      res,
      HTTP_CODES.CREATED,
      "success",
      "Excel file generation request created successfully",
      reportFile
    );
  } catch (error) {
    console.error("Error in generateExcel:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Get Excel file status
exports.getStatus = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId validation
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid report file ID format"
      );
    }

    const reportFile = await ReportFilesModel.findById(id)
      .populate("reportId", "companyName reportType")
      .populate("requestedBy", "name email");

    if (!reportFile) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Report file not found"
      );
    }

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Report file status retrieved successfully",
      reportFile
    );
  } catch (error) {
    console.error("Error in getStatus:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// List user's Excel file requests
exports.list = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;

    // User validation
    if (!req.user || !req.user.email) {
      return httpResponse(
        res,
        HTTP_CODES.UNAUTHORIZED,
        "error",
        "User not authenticated"
      );
    }

    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    const query = { requestedBy: user._id };
    if (status) {
      query.status = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const [docs, totalDocs] = await Promise.all([
      ReportFilesModel.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate("reportId", "companyName reportType")
        .populate("requestedBy", "name email"),
      ReportFilesModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(totalDocs / parseInt(limit));

    const result = {
      docs,
      totalDocs,
      limit: parseInt(limit),
      totalPages,
      page: parseInt(page),
      pagingCounter: skip + 1,
      hasPrevPage: parseInt(page) > 1,
      hasNextPage: parseInt(page) < totalPages,
      prevPage: parseInt(page) > 1 ? parseInt(page) - 1 : null,
      nextPage: parseInt(page) < totalPages ? parseInt(page) + 1 : null,
    };

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Report files retrieved successfully",
      result
    );
  } catch (error) {
    console.error("Error in list:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Download Excel file
exports.download = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId validation
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid report file ID format"
      );
    }

    const reportFile = await ReportFilesModel.findById(id).populate(
      "reportId",
      "companyName reportType"
    );

    if (!reportFile) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Report file not found"
      );
    }

    if (reportFile.status !== "ready") {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        `File is not ready for download. Current status: ${reportFile.status}`
      );
    }

    if (!reportFile.fileUrl) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "File URL not found"
      );
    }

    // Redirect to Azure blob
    res.redirect(reportFile.fileUrl);
  } catch (error) {
    console.error("Error in download:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
