// Her training sayfası için detaylı kurs mock datası
export const trainingPageCourses = {
  'core-ai-essentials': [
    {
      id: 'core-ai-001',
      translations: {
        en: {
          title: 'Introduction to Python',
          description:
            'Gain foundational Python skills by creating programs, handling data, and building interactive applications.',
          buttonText: 'Learn more', 
        },
        de: {
          title: 'Introduction to Python',
          description:
            'Gain foundational Python skills by creating programs, handling data, and building interactive applications.',
          buttonText: 'Mehr erfahren', 
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/Pexels-divinetechygirl.jpg',
      buttonType: 'URL',
      buttonURL: '/course/67d7f8482238849a6dc2ab26',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-002',
      translations: {
        en: {
          title: 'Python for Machine Learning',
          description:
            'Improve Python skills and learn machine learning basics to solve real-world problems and take the first step into AI.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Python for Machine Learning',
          description:
            'Improve Python skills and learn machine learning basics to solve real-world problems and take the first step into AI.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/Pexels-markusspiske-177598.jpg',
      buttonType: 'URL',
      buttonURL: '/course/67d7f8522238849a6dc2d48d',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-003',
      translations: {
        en: {
          title: 'Introduction to Machine Learning',
          description:
            'Build a strong foundation in machine learning, covering key terms, algorithms and performance evaluation.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Introduction to Machine Learning',
          description:
            'Build a strong foundation in machine learning, covering key terms, algorithms and performance evaluation.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/Pexels-danny-meneses.jpg',
      buttonType: 'URL',
      buttonURL: '/course/67d7f79d2238849a6dc1ad5b',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-004',
      translations: {
        en: {
          title: 'Introduction to Deep Learning',
          description:
            'Learn deep learning fundamentals, model structures, and techniques to build and optimize models for real-world applications.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Introduction to Deep Learning',
          description:
            'Learn deep learning fundamentals, model structures, and techniques to build and optimize models for real-world applications.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/Pexels-pixabay-276452.jpg',
      buttonType: 'URL',
      buttonURL: '/course/67d7f5a52238849a6dc07518',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-005',
      translations: {
        en: {
          title: 'Fundamental AI Concepts',
          description:
            'The beginner-friendly course explains what AI is and how it’s used in areas like generative AI, computer vision, and NLP. Learn about responsible AI and real-world applications.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Grundlegende AI-Konzepte',
          description:
            'Dieser einsteigerfreundliche Kurs erklärt, was AI ist und wie sie in Bereichen wie generativer AI, Computer Vision und NLP eingesetzt wird. Sie lernen auch, was verantwortungsvoller AI-Einsatz bedeutet und wie AI in der Praxis genutzt wird.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/fundamental-ai.jpeg',
      buttonType: 'URL',
      buttonURL: '/course/68495f76f0694ec8527493ea',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-006',
      translations: {
        en: {
          title: 'Fundamentals of Azure AI services',
          description:
            'Core Azure AI services and how to use them in applications. Learn how to create resources, access services through the Azure portal, and manage authentication using keys and endpoints.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erste Schritte mit Azure AI-Diensten',
          description:
            'Dieser Kurs stellt die zentralen Azure AI-Dienste vor und zeigt, wie sie in Anwendungen verwendet werden können. Sie lernen, wie Sie Ressourcen erstellen, Dienste im Azure-Portal aufrufen und die Authentifizierung über Schlüssel und Endpunkte verwalten.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/artificial-intelligence.webp',
      buttonType: 'URL',
      buttonURL: '/course/684a8a27f0694ec85274ac88',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-007',
      translations: {
        en: {
          title: 'Describe cloud computing',
          description:
            'Explore core cloud computing concepts, deployment models, and the shared responsibility model. Learn cloud types and compare pricing options.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Beschreiben von Cloud Computing',
          description:
            'Entdecken Sie zentrale Konzepte des Cloud Computing, verschiedene Bereitstellungsmodelle sowie das Modell der gemeinsamen Verantwortung. Lernen Sie die verschiedenen Cloud-Typen kennen und vergleichen Sie Preisoptionen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/describe_cloud_computing.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68526d42f0694ec8527564af',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-008',
      translations: {
        en: {
          title: 'Introduction to Azure virtual machines',
          description:
            'Discover how to create, manage, and maintain Azure Virtual Machines. Explore setup checklists, deployment options, and tools for backup and availability.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Einführung in Azure Virtual Machines',
          description:
            'Erfahren Sie, wie Sie Azure Virtual Machines erstellen, verwalten und warten. Entdecken Sie Checklisten für die Einrichtung, Bereitstellungsoptionen und Tools für Backup und Verfügbarkeit.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/introduction_to_azure_virtual_machines.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685277caf0694ec8527567f9',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-009',
      translations: {
        en: {
          title: 'Introduction to building copilots for startups',
          description:
            'Explore how business can build copilots using the Azure Copilot Stack. Learn core concepts, real use cases, and best practices to create AI-powered solutions that deliver customer value.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Einführung in die Erstellung von Copiloten für Startups',
          description:
            'Erfahren Sie, wie Unternehmen mit dem Azure Copilot Stack eigene Copilots entwickeln können. Lernen Sie die wichtigsten Konzepte, praxisnahe Anwendungsfälle und bewährte Methoden kennen, um KI-gestützte Lösungen zu schaffen, die echten Mehrwert für Kundinnen und Kunden bieten.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/introduction_to_building_copilots_for_startups.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852977af0694ec852757367',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-010',
      translations: {
        en: {
          title: 'Develop an AI app with the Azure AI Foundry SDK',
          description:
            'Use the Azure AI Foundry SDK to build AI-powered chat apps. Work with project connections, explore SDK capabilities, and create conversational experiences with Python or C#.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Entwickeln einer KI-App mit dem Azure AI Foundry SDK',
          description:
            'Nutzen Sie das Azure AI Foundry SDK, um KI-gestützte Chat-Anwendungen zu erstellen. Arbeiten Sie mit Projektverbindungen, entdecken Sie die Funktionen des SDK und gestalten Sie Gesprächserlebnisse mit Python oder C#.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/develop_an_ai_app_with_the_azure_ai_foundry_sdk.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68527e03f0694ec85275694e',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-011',
      translations: {
        en: {
          title: 'Use AI for everyday tasks',
          description:
            'Learn how generative AI can simplify everyday tasks. Discover practical applications, write better prompts, and boost productivity through AI tools.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Verwenden von KI für alltägliche Aufgaben',
          description:
            'Erfahren Sie, wie generative KI alltägliche Aufgaben vereinfachen kann. Entdecken Sie praktische Anwendungen, formulieren Sie effektivere Eingaben und steigern Sie die Produktivität mit KI-Tools.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/use_ai_for_everyday_tasks.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852810af0694ec852756a8b',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-012',
      translations: {
        en: {
          title: 'Explore AI for All',
          description:
            'Learn how AI is driving  change across accessibility, job roles, and humanitarian efforts. Review real examples and case studies showing AI\'s impact in diverse industries and communities.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erkunden von KI für alle',
          description:
            'Erfahren Sie, wie KI Veränderungen in den Bereichen Barrierefreiheit, Berufsrollen und humanitäre Einsätze vorantreibt. Betrachten Sie reale Beispiele und Fallstudien, die den Einfluss von KI in verschiedenen Branchen und Gemeinschaften zeigen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/explore_ai_for_all.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685282e5f0694ec852756bad',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-013',
      translations: {
        en: {
          title: 'Design an Azure compute solution',
          description:
            'Learn how to design end-to-end compute solutions using Azure services like VMs, App Service, Kubernetes, and Functions.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Entwerfen einer Azure-Computelösung',
          description:
            'Erfahren Sie, wie Sie End-to-End-Compute-Lösungen mit Azure-Diensten wie VMs, App Service, Kubernetes und Functions entwerfen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/design_an_azure_compute_solution.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68528a91f0694ec852756d2d',
      newTab: false,
      locked: false,
    },
    { 
      id: 'core-ai-014',
      translations: {
        en: {
          title: 'Describe the benefits of using cloud services',
          description:
            'Discover how cloud services improve scalability, reliability, security, and manageability. Learn how these benefits support business continuity and simplify IT operations.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Nutzen der Verwendung von Clouddiensten',
          description:
            'Erfahren Sie, wie Cloud-Dienste Skalierbarkeit, Zuverlässigkeit, Sicherheit und Verwaltung verbessern. Lernen Sie, wie diese Vorteile die Geschäftskontinuität unterstützen und IT-Betriebe vereinfachen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/describe_the_benefits_of_using_cloud_services.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68525bd7f0694ec852755ae4 ',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-015',
      translations: {
        en: {
          title: 'Describe cloud service types',
          description:
            'Break down the differences between IaaS, PaaS, and SaaS. Identify when to use each cloud service type and how they support different business and technical needs.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Beschreiben der Clouddiensttypen',
          description:
            'Analysieren Sie die Unterschiede zwischen IaaS, PaaS und SaaS. Erkennen Sie, wann welcher Cloud-Diensttyp eingesetzt werden sollte und wie sie verschiedene geschäftliche und technische Anforderungen unterstützen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/describe_cloud_service_types.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68526344f0694ec852755d1a',
      newTab: false,
      locked: false,
    },
    {
      id: 'core-ai-016',
      translations: {
        en: {
          title: 'Get started with GitHub Copilot',
          description:
            'Get started with GitHub Copilot and Copilot Chat in Visual Studio Code. Learn how to set it up, explore key features, and boost your coding with AI-powered suggestions.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erste Schritte mit GitHub Copilot',
          description:
            'Starten Sie mit GitHub Copilot und Copilot Chat in Visual Studio Code. Erfahren Sie, wie Sie es einrichten, wichtige Funktionen entdecken und Ihr Coding mit KI-gestützten Vorschlägen verbessern.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/get_started_with_github_copilot.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68527235f0694ec85275662b',
      newTab: false,
      locked: false,
    },
  ],
  'prompt-engineering-llm-fine-tuning': [
    {
      id: 'prompt-001',
      translations: {
        en: {
          title: 'Introduction to large language models',
          description:
            'Learn what LLMs are, how they work, and when to use them, along with core terms like prompts and tokens. Choose and apply the right model for different tasks.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Einführung in große Sprachmodelle',
          description:
            'Dieser Kurs führt in die Grundlagen der künstlichen Intelligenz ein und konzentriert sich auf grosse Sprachmodelle (LLMs). Sie lernen, was LLMs sind, wie sie funktionieren und wann sie eingesetzt werden sollten – inklusive wichtiger Begriffe wie Prompts und Tokens. Am Ende wissen Sie, wie Sie das passende Modell für verschiedene Aufgaben auswählen und anwenden.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/llm-aibs.jpg',
      buttonType: 'URL',
      buttonURL: '/course/684a9322f0694ec85274aff3',
      newTab: false,
      locked: false,
    },
    {
      id: 'prompt-002',
      translations: {
        en: {
          title:
            'Understand the Transformer architecture and explore large language models in Azure Machine Learning',
          description:
            'Explore the evolution of natural language processing and how the Transformer architecture enabled the rise of large language models. Learn to work with foundation models in Azure Machine Learning, including how to deploy and test them.',
          buttonText: 'Learn more', 
        },
        de: {
          title:
            'Grundlegendes zur Transformatorarchitektur und Erkunden großer Sprachmodelle in Azure Machine Learning',
          description:
            'Erkunden Sie die Entwicklung der Sprachverarbeitung und wie die Transformer-Architektur den Weg für grosse Sprachmodelle geebnet hat. Sie lernen, wie Sie Foundation-Modelle in Azure Machine Learning einsetzen, testen und bereitstellen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/understand_the_transformer_architecture_and_explore_large_language_models_in_azure_machine_learning.jpg',
      buttonType: 'URL',
      buttonURL: '/course/684ad429f0694ec85274d1b4',
      newTab: false,
      locked: false,
    },
    {
      id: 'prompt-003',
      translations: {
        en: {
          title:
            'Fundamentals of conversational language understanding',
          description:
            'Use Azure AI Language to develop language-aware applications. Learn key concepts like intents and utterances, and train a conversational model for natural language understanding.',
          buttonText: 'Learn more', 
        },
        de: {
          title:
            'Grundlagen zu Conversational Language Understanding',
          description:
            'Nutzen Sie Azure AI Language, um sprachbewusste Anwendungen zu entwickeln. Lernen Sie zentrale Konzepte wie Intents und Äusserungen kennen und trainieren Sie ein konversationelles Modell für das Verständnis natürlicher Sprache.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/fundamentals_of_conversational_language_understanding.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852732ef0694ec85275668b',
      newTab: false,
      locked: false,
    },
    {
      id: 'prompt-004',
      translations: {
        en: {
          title:
            'Find the best classification model with Automated Machine Learning',
          description:
            'Use Automated Machine Learning (AutoML) to find the best classification model with the Azure ML Python SDK. Learn how to prepare data, configure experiments, and evaluate results effectively.',
          buttonText: 'Learn more', 
        },
        de: {
          title:
            'Suchen des besten Klassifizierungsmodells mit automatisiertem Maschinellem Lerneng',
          description:
            'Verwenden Sie Automated Machine Learning (AutoML), um mit dem Azure ML Python SDK das beste Klassifikationsmodell zu finden. Erfahren Sie, wie Sie Daten vorbereiten, Experimente konfigurieren und Ergebnisse effizient auswerten.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/find_the_best_classification_model_with_automated_machine_learning.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685e96306ffc600e04cd72a1',
      newTab: false,
      locked: false,
    },
  ],
  'genai-software-engineering-workflows': [
    {
      id: 'genai-sw-001',
      translations: {
        en: {
          title: 'GitHub Copilot Fundamentals',
          description:
            'GitHub provides an AI-powered developer platform to build, scale, and deliver secure software.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'GitHub Copilot Fundamentals',
          description:
            'GitHub provides an AI-powered developer platform to build, scale, and deliver secure software.',
          buttonText: 'Mehr erfahren', 
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/copilot-chat.webp',
      buttonType: 'URL',
      buttonURL: '/course/67d7f7272238849a6dc17218',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-002',
      translations: {
        en: {
          title: 'Responsible AI with GitHub Copilot',
          description:
            'Learn how to use GitHub Copilot responsibly by understanding its limitations and ethical considerations. Best practices for safe AI usage, including transparency, accountability, and aligning AI-generated code with project goals.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Verantwortungsvolle KI mit GitHub Copilot',
          description:
            'Lernen Sie, wie Sie GitHub Copilot verantwortungsvoll nutzen, indem Sie dessen Grenzen und ethische Aspekte verstehen. Der Kurs behandelt Best Practices für sicheren KI-Einsatz, darunter Transparenz, Verantwortlichkeit und die Ausrichtung KI-generierten Codes auf Projektziele.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/integrating_genai_into_software_engineering_workflow.webp',
      buttonType: 'URL',
      buttonURL: '/course/684a94f4f0694ec85274b166',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-003',
      translations: {
        en: {
          title: 'Explore responsible AI',
          description:
            'The core principles of responsible AI, including ethics, transparency, and data privacy. Examine real-world concerns like deepfakes, algorithmic bias, and legal risks to better understand the global impact of human-AI interaction.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Verantwortungsvolle KI erkunden',
          description:
            'Dieser Kurs vermittelt die Grundprinzipien verantwortungsvoller KI – wie Ethik, Transparenz und Datenschutz. Sie analysieren reale Risiken wie Deepfakes, algorithmische Voreingenommenheit und rechtliche Herausforderungen, um die globale Wirkung der Mensch-KI-Interaktion besser zu verstehen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/integrating_genai_into_software_engineering_workflows.jpeg',
      buttonType: 'URL',
      buttonURL: '/course/684aba6cf0694ec85274c4c0',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-004',
      translations: {
        en: {
          title: 'Implement a responsible generative AI solution in Azure AI Foundry',
          description:
            'Develop generative AI solutions while identifying and mitigating risks like harmful content. Learn planning, measuring, and managing responsible AI systems for safe deployment and long-term use.',
          buttonText: 'Learn more',
        },
        de: {
          title:
            'Implementieren einer verantwortungsvollen generativen KI-Lösung in Azure AI Foundry',
          description:
            'Lernen Sie, wie Sie generative KI-Lösungen entwickeln und gleichzeitig Risiken wie schädliche Inhalte erkennen und minimieren. Der Kurs führt Sie durch Planung, Bewertung und Management von verantwortungsvollen KI-Systemen für eine sichere Umsetzung.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/implement_a_responsible_generative_ai_solution_in_azure_ai_foundry.webp',
      buttonType: 'URL',
      buttonURL: '/course/684a85a1f0694ec85274a525',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-005',
      translations: {
        en: {
          title: 'Fine-tune a foundation model with Azure Machine Learning',
          description:
            'Fine-tune pretrained large language models for specific tasks using Azure Machine Learning. Learn to select models, customize them in Azure ML Studio, and evaluate and deploy your results.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Optimieren eines Foundation-Modells mit Azure Machine Learning',
          description:
            'Lernen Sie, wie Sie vortrainierte grosse Sprachmodelle für spezifische Aufgaben mit Azure Machine Learning feinabstimmen. Der Kurs führt Sie durch Modellauswahl, Anpassung im ML Studio und die Auswertung und Bereitstellung der Ergebnisse.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/fine_tune_a_foundation_model_with_azure_machine_learning.jpg',
      buttonType: 'URL',
      buttonURL: '/course/684be35ef0694ec85274e97a',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-006',
      translations: {
        en: {
          title: 'Get started with prompt flow to develop Large Language Model (LLM) apps',
          description:
            'Use prompt flow in Azure to design, build, and manage applications powered by large language models. LLM app development lifecycle, key components of prompt flow, and how to monitor and optimize your workflows.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erste Schritte mit dem Aufforderungsfluss zum Entwickeln von LLM-Apps',
          description:
            'Lernen Sie, wie Sie mit Prompt Flow in Azure Anwendungen mit grossen Sprachmodellen entwickeln, steuern und optimieren. Der Kurs behandelt den Entwicklungszyklus von LLM-Anwendungen und die zentralen Komponenten von Prompt Flow.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/get_started_with_prompt_flow_to_develop_large_language_model_llm_apps.jpeg',
      buttonType: 'URL',
      buttonURL: '/course/684a94e8f0694ec85274b158',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-007',
      translations: {
        en: {
          title: 'Implement GitHub Actions for Azure',
          description:
            'Use GitHub Actions to automate deployments and workflows between GitHub and Azure. Configure service principals and publish a web app to Azure App Service with streamlined CI/CD.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Implementieren von GitHub Actions für Azure',
          description:
            'Nutzen Sie GitHub Actions, um Deployments und Workflows zwischen GitHub und Azure zu automatisieren. Konfigurieren Sie Service Principals und veröffentlichen Sie eine Web-App mit optimiertem CI/CD auf Azure App Service.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/implement_github_actions_for_azure.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852bd99f0694ec852757f58',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-008',
      translations: {
        en: {
          title: 'Configure code scanning on GitHub',
          description:
            'Introduction to code scanning in GitHub using CodeQL, third-party tools, and GitHub Actions. Learn how to configure scans by event triggers or schedules to improve code security and quality.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Konfigurieren von Codescans für GitHub',
          description:
            'Einführung in das Code-Scanning in GitHub mit CodeQL, Drittanbieter-Tools und GitHub Actions. Erfahren Sie, wie Sie Scans durch Ereignisauslöser oder Zeitpläne konfigurieren, um die Code-Sicherheit und -Qualität zu verbessern.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/configure_code_scanning_on_github.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852c378f0694ec85275807e',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-009',
      translations: {
        en: {
          title: 'Automate development tasks by using GitHub Actions',
          description:
            'Create and run GitHub Actions to automate development workflows. Learn action types, build a container action, and trigger workflows with events like code pushes.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Automatisieren von Entwicklungsaufgaben mit GitHub Actions',
          description:
            'Erstellen und starten Sie GitHub Actions, um Entwicklungs-Workflows zu automatisieren. Lernen Sie verschiedene Aktionstypen kennen, erstellen Sie eine Container-Aktion und lösen Sie Workflows durch Ereignisse wie Code-Pushes aus.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/automate_development_tasks_by_using_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852c84af0694ec852758ad1',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-010',
      translations: {
        en: {
          title: 'Build continuous integration (CI) workflows by using GitHub Actions',
          description:
            'Create and customize CI workflows using GitHub Actions. Build, test, and debug projects while automating integration tasks for a smoother development process.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erstellen kontinuierlicher Integrationsworkflows mithilfe von GitHub-Aktionen',
          description:
            'Erstellen und individualisieren Sie CI-Workflows mit GitHub Actions. Bauen, testen und debuggen Sie Projekte und automatisieren Sie Integrationsaufgaben für einen reibungsloseren Entwicklungsprozess.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/build_continuous_integration_ci_workflows_by_using_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852d98bf0694ec852759c71',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-011',
      translations: {
        en: {
          title: 'Learn continuous integration with GitHub Actions',
          description:
            'Discover how to set up continuous integration using GitHub Actions. Learn to automate workflows, share artifacts, and secure your pipeline using encrypted variables and secrets.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Lernen Sie kontinuierliche Integration mit GitHub Actions',
          description:
            'Entdecken Sie, wie Sie Continuous Integration mit GitHub Actions einrichten. Automatisieren Sie Workflows, teilen Sie Artefakte und sichern Sie Ihre Pipeline mit verschlüsselten Variablen und Geheimnissen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/learn_continuous_integration_with_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852c311f0694ec852758054',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-012',
      translations: {
        en: {
          title: 'Deploy a cloud-native .NET microservice automatically with GitHub Actions and Azure Pipelines',
          description:
            'Automate the deployment of a .NET microservice to Azure Kubernetes Service (AKS) using GitHub Actions and Azure Pipelines. Learn to build containers, manage secrets, deploy updates, and handle rollbacks with CI/CD workflows.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Automatisches Bereitstellen eines cloudeigenen .NET Microservice mit GitHub Actions und Azure Pipelines',
          description:
            'Automatisieren Sie die Bereitstellung eines .NET-Microservices auf Azure Kubernetes Service (AKS) mit GitHub Actions und Azure Pipelines. Lernen Sie, Container zu erstellen, Geheimnisse zu verwalten, Updates bereitzustellen und Rollbacks mit CI/CD-Workflows durchzuführen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/deploy_a_cloud_native_dotnet_microservice_automatically_with_github_actions_and_azure_pipelines.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852e2f6f0694ec85275a8a8',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-013',
      translations: {
        en: {
          title: 'Rapidly develop and deploy Java apps using GitHub Actions or Azure Pipelines',
          description:
            'Efficiently set up Azure infrastructure and deploy Java applications using Terraform and CI/CD pipelines. Learn how to configure your environment and streamline deployment using GitHub Actions or Azure Pipelines.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Schnelles Entwickeln und Bereitstellen von Java-Apps mithilfe von GitHub Actions oder Azure Pipelines',
          description:
            'Richten Sie effizient Azure-Infrastrukturen ein und stellen Sie Java-Anwendungen mit Terraform und CI/CD-Pipelines bereit. Erfahren Sie, wie Sie Ihre Umgebung konfigurieren und die Bereitstellung mit GitHub Actions oder Azure Pipelines vereinfachen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/rapidly_develop_and_deploy_java_apps_using_github_actions_or_azure_pipelines.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852bbecf0694ec852757efe',
      newTab: false,
      locked: false,
    },
    {
      id: 'genai-sw-014',
      translations: {
        en: {
          title: 'Implement GitHub Actions',
          description:
            'Set up and manage GitHub Actions workflows to automate development tasks. Work with variables, scripts, and expressions to control workflow behavior efficiently.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Implementieren von GitHub-Aktionen',
          description:
            'Richten Sie GitHub Actions Workflows ein und verwalten Sie diese, um Entwicklungsaufgaben zu automatisieren. Arbeiten Sie mit Variablen, Skripten und Ausdrücken, um das Workflow-Verhalten effizient zu steuern.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/implement_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685bae0bf0694ec8527657a1',
      newTab: false,
      locked: false,
    },
  ],
  'autonomous-ai-agents': [
    {
      id: 'agents-001',
      translations: {
        en: {
          title: 'Get started with AI agent development on Azure',
          description:
            'The fundamentals of AI agents and how they can be built using Azure AI services. Learn key concepts, explore development options, and create your first agent using the Azure AI Foundry portal.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Grundlagen von KI-Agents in Azure',
          description:
            'Dieser Kurs vermittelt die Grundlagen von KI-Agenten und wie sie mit Azure AI-Diensten erstellt werden können. Sie lernen zentrale Konzepte kennen, erkunden Entwicklungsoptionen und bauen Ihren ersten Agenten mit dem Azure AI Foundry-Portal.',
          buttonText: 'Mehr erfahren', 
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/get_started_with_ai_agent_development_on_azure.png',
      buttonType: 'URL',
      buttonURL: '/course/684a9363f0694ec85274b0ae',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-002',
      translations: {
        en: {
          title: 'Develop an AI agent with Azure AI Foundry Agent Service',
          description:
            'Build intelligent AI agents using Azure’s Foundry Agent Service. The core features of the service and guides you through creating and integrating agents into your own applications.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Entwickeln eines KI-Agents mit dem Azure AI Foundry Agent Service',
          description:
            'Lernen Sie, wie Sie intelligente KI-Agenten mit dem Azure Foundry Agent Service entwickeln. Der Kurs zeigt die wichtigsten Funktionen und begleitet Sie bei der Erstellung und Integration von Agenten in Ihre Anwendungen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/develop_an_ai_agent_with_azure_ai_foundry_agent_service.jpeg',
      buttonType: 'URL',
      buttonURL: '/course/684ac01af0694ec85274c5b3',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-003',
      translations: {
        en: {
          title: 'Integrate custom tools into your agent',
          description:
            'Enhance your AI agents by integrating custom tools using Azure AI Foundry. Learn the benefits and implementation options of custom tools, and walks you through building an agent with extended capabilities.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Integrieren von benutzerdefinierten Tools in Ihren Agent',
          description:
            'Erweitern Sie Ihre KI-Agenten, indem Sie benutzerdefinierte Tools mit Azure AI Foundry integrieren. Der Kurs behandelt die Vorteile, Implementierungsmöglichkeiten und zeigt, wie Sie Agenten mit erweiterten Funktionen entwickeln.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/integrate_custom_tools_into_your_agent_1024x576.jpg',
      buttonType: 'URL',
      buttonURL: '/course/684bdb39f0694ec85274e82f',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-004',
      translations: {
        en: {
          title: 'Develop an AI agent with Semantic Kernel',
          description:
            'Build AI agents using the Semantic Kernel SDK in Azure AI Foundry. Learn to connect projects, create agents, and integrate plugin functions to extend their capabilities.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Entwickeln eines KI-Agents mit semantischem Kernel',
          description:
            'Dieser Kurs zeigt, wie Sie KI-Agenten mit dem Semantic Kernel SDK in Azure AI Foundry entwickeln. Sie lernen, Projekte zu verbinden, Agenten zu erstellen und Plugin-Funktionen zu integrieren.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/develop_an_ai_agent_with_semantic_kernel.png',
      buttonType: 'URL',
      buttonURL: '/course/684a8b60f0694ec85274acd0',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-005',
      translations: {
        en: {
          title: 'Orchestrate a multi-agent solution using Semantic Kernel',
          description:
            'Build and coordinate multiple AI agents using the Semantic Kernel SDK. Learn creating agent collaboration strategies, including custom selection and chat termination logic.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Orchestrate a multi-agent solution using Semantic Kernel',
          description:
            'Build and coordinate multiple AI agents using the Semantic Kernel SDK. Learn creating agent collaboration strategies, including custom selection and chat termination logic.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/orchestrate_a_multi_agent_solution_using_semantic_kernel.png',
      buttonType: 'URL',
      buttonURL: '/course/684a9827f0694ec85274b1e3',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-006',
      translations: {
        en: {
          title: 'Manage agents in Microsoft Copilot Studio',
          description:
            'Create, configure, and manage AI agents tailored to business needs using Microsoft Copilot Studio. Learn agent permissions, entities, user authentication, and best practices for administration and security.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Agents in Microsoft Copilot Studio verwalten',
          description:
            'Lernen Sie, wie Sie KI-Agenten in Microsoft Copilot Studio erstellen, konfigurieren und verwalten. Der Kurs behandelt Berechtigungen, Entitäten, Nutzer-Authentifizierung und Verwaltungsrichtlinien.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/manage_agents_in_microsoft_copilot_studio.webp',
      buttonType: 'URL',
      buttonURL: '/course/684bd322f0694ec85274e0cf',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-007',
      translations: {
        en: {
          title: 'Set up a Microsoft Copilot Studio agent for voice',
          description:
            'Enable voice capabilities for AI agents in Microsoft Copilot Studio. Learn installing required extensions, configuring IVR features, and connecting your agent to a voice service provider.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Einen Microsoft Copilot Studio-Agent für Sprache einrichten',
          description:
            'Lernen Sie, wie Sie Sprachfunktionen für KI-Agenten in Microsoft Copilot Studio aktivieren. Der Kurs zeigt, wie Sie Erweiterungen installieren, IVR-Funktionen einrichten und den Agenten mit einem Sprachdienst verbinden.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/set_up_a_microsoft_copilot_studio_agent_for_voice.jpg',
      buttonType: 'URL',
      buttonURL: '/course/684a9d07f0694ec85274b542',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-008',
      translations: {
        en: {
          title: 'A guide to artificial intelligence',
          description:
            'Understand key AI and machine learning concepts, including learning types and neural networks. See how different approaches apply to data and model refinement.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Ein Leitfaden zur künstlichen Intelligenz',
          description:
            'Verstehen Sie zentrale Konzepte der künstlichen Intelligenz und des maschinellen Lernens, einschliesslich Lernarten und neuronaler Netzwerke. Erfahren Sie, wie verschiedene Ansätze auf Daten und die Modellverfeinerung angewendet werden.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/a_guide_to_artificial_intelligence.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68528d80f0694ec852756e27',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-009',
      translations: {
        en: {
          title: 'Introduction to GitHub Copilot Business',
          description:
            'Understand the differences between GitHub Copilot Business and Individual plans, review practical use cases, and learn how to enable Copilot Business in your organization.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Einführung in GitHub Copilot Business',
          description:
            'Verstehen Sie die Unterschiede zwischen den GitHub Copilot Business- und Individual-Plänen, prüfen Sie praktische Anwendungsfälle und erfahren Sie, wie Sie Copilot Business in Ihrer Organisation aktivieren können.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/introduction_to_github_copilot_business.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685290d5f0694ec85275720c',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-010',
      translations: {
        en: {
          title: 'Implement code improvements using GitHub Copilot tools',
          description:
            'Use GitHub Copilot Chat to enhance your codebase with smart suggestions. Focus on improving code quality, reliability, and performance through real-time refactoring and review tools.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Implementieren von Codeverbesserungen mithilfe von GitHub Copilot-Tools',
          description:
            'Nutzen Sie GitHub Copilot Chat, um Ihren Code mit intelligenten Vorschlägen zu verbessern. Konzentrieren Sie sich auf die Steigerung von Codequalität, Zuverlässigkeit und Leistung durch Echtzeit-Refactoring und Review-Tools.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/implement_code_improvements_using_github_copilot_tools.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68528343f0694ec852756bd1',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-011',
      translations: {
        en: {
          title: 'Generate documentation using GitHub Copilot tools',
          description:
            'Use GitHub Copilot Chat to document your codebase more efficiently. Generate clear code explanations, inline comments, and project documentation directly within Visual Studio Code.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Generieren einer Dokumentation mithilfe von GitHub Copilot-Tools',
          description:
            'Nutzen Sie GitHub Copilot Chat, um Ihre Codebasis effizienter zu dokumentieren. Erstellen Sie klare Codeerklärungen, Inline-Kommentare und Projektdokumentationen direkt in Visual Studio Code.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/generate_documentation_using_github_copilot_tools.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68528950f0694ec852756cf5',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-012',
      translations: {
        en: {
          title: 'Develop code features using GitHub Copilot tools',
          description:
            'Review and test your code with GitHub Copilot Chat in Visual Studio Code. Generate test cases, catch bugs, and improve code quality with AI-powered assistance.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Entwickeln von Codefeatures mithilfe von GitHub Copilot-Tools',
          description:
            'Überprüfen und testen Sie Ihren Code mit GitHub Copilot Chat in Visual Studio Code. Erstellen Sie Testfälle, finden Sie Fehler und verbessern Sie die Codequalität mit KI-gestützter Unterstützung.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/develop_code_features_using_github_copilot_tools.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852b72ef0694ec852757d77',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-013',
      translations: {
        en: {
          title: 'Introduction to GitHub Copilot Enterprise',
          description:
            'Explore key use cases for GitHub Copilot Enterprise, learn how to get started and see how to best use it within your organization.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Einführung in GitHub Copilot Enterprise',
          description:
            'Entdecken Sie wichtige Anwendungsfälle für GitHub Copilot Enterprise, erfahren Sie, wie Sie starten können, und lernen Sie, wie Sie es optimal in Ihrer Organisation einsetzen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/introduction_to_github_copilot_enterprise.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852ba04f0694ec852757e2d',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-014',
      translations: {
        en: {
          title: 'Introduction to GitHub Actions',
          description:
            'Get started with GitHub Actions and learn how to create and manage workflows. Understand how actions support automation and streamline development processes.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Einführung in GitHub-Aktionen',
          description:
            'Starten Sie mit GitHub Actions und erfahren Sie, wie Sie Workflows erstellen und verwalten. Verstehen Sie, wie Actions Automatisierung unterstützen und Entwicklungsprozesse vereinfachen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/introduction_to_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852d663f0694ec852759bda',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-015',
      translations: {
        en: {
          title: 'Deliver with DevOps',
          description:
            'Discover how to implement continuous delivery with GitHub Actions and Infrastructure as Code (IaC). Learn CI/CD fundamentals, progressive deployment strategies, and DevOps best practices.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Bereitstellen mit DevOps',
          description:
            'Entdecken Sie, wie Sie Continuous Delivery mit GitHub Actions und Infrastructure as Code (IaC) umsetzen. Lernen Sie die Grundlagen von CI/CD, progressive Bereitstellungsstrategien und bewährte DevOps-Praktiken kennen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/deliver_with_devops.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852dd81f0694ec852759f4c',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-016',
      translations: {
        en: {
          title: 'Create and publish custom GitHub actions',
          description:
            'Create your own GitHub Actions by defining custom workflows, setting up metadata, and writing JavaScript-based actions. Learn how to document and publish your actions for reuse or sharing through the GitHub Marketplace.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erstellen und Veröffentlichen benutzerdefinierter GitHub-Aktionen',
          description:
            'Erstellen Sie eigene GitHub Actions, indem Sie benutzerdefinierte Workflows definieren, Metadaten festlegen und JavaScript-basierte Actions schreiben. Erfahren Sie, wie Sie Ihre Actions dokumentieren und für die Wiederverwendung oder Weitergabe über den GitHub Marketplace veröffentlichen können.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/create_and_publish_custom_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6853d2ebf0694ec85275c1ae',
      newTab: false,
      locked: false,
    },
    {
      id: 'agents-016',
      translations: {
        en: {
          title: 'Automate GitHub by using GitHub Script',
          description:
            'Use GitHub Script to interact with the GitHub API and automate tasks like commenting on issues, updating project boards with Octokit, and managing workflow execution.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Automatisieren von GitHub mithilfe von GitHub Script',
          description:
            'Verwenden Sie GitHub Script, um mit der GitHub API zu interagieren und Aufgaben wie das Kommentieren von Issues, das Aktualisieren von Projektboards mit Octokit und das Verwalten der Workflow-Ausführung zu automatisieren.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/automate_github_by_using_github_script.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852b881f0694ec852757deb',
      newTab: false,
      locked: false,
    },
  ],
  'scaling-genai-enterprise': [
    {
      id: 'enterprise-001',
      translations: {
        en: {
          title: 'Use Azure AI Services for Language in a Microsoft Copilot Studio',
          description:
            'Enhance your Copilot Studio agent by integrating Azure AI Services for Language. Learn setting up the service, connecting it with your agent using Power Automate, and expanding your agent’s language capabilities.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Azure KI Services for Language in einem Microsoft Copilot Studio verwenden',
          description:
            'Lernen Sie, wie Sie Azure AI Services for Language in Copilot Studio integrieren. Der Kurs führt Sie durch die Einrichtung des Dienstes, den Einsatz mit Power Automate und die Erweiterung der Sprachfähigkeiten Ihres Agenten.',
          buttonText: 'Mehr erfahren',
        },
      },
      duration: '5h 45m',
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/use_azure_ai_services_for_language_in_a_microsoft_copilot_studio.jpeg',
      buttonType: 'URL',
      buttonURL: '/course/684bf1b9f0694ec85274fb90',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-002',
      translations: {
        en: {
          title: 'Plan and prepare to develop AI solutions on Azure',
          description:
            "Plan AI projects using Azure's tools and services. Explore common AI capabilities, choose the right services and SDKs, and consider responsible AI practices to ensure a solid development foundation.",
          buttonText: 'Learn more',
        },
        de: {
          title: 'Planen und Vorbereiten der Entwicklung von KI-Lösungen in Azure',
          description:
            'Dieser Kurs hilft Ihnen, KI-Projekte mit den Tools und Diensten von Azure effizient zu planen. Sie lernen gängige KI-Funktionen kennen, wählen passende Services und SDKs aus und berücksichtigen ethische Aspekte für einen soliden Projektstart.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/plan_and_prepare_to_develop_ai_solutions_on_azure.jpeg',
      buttonType: 'URL',
      buttonURL: '/course/684a9e18f0694ec85274b5f5',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-003',
      translations: {
        en: {
          title: 'Create and consume Azure AI services',
          description:
            'Provision Azure AI services and integrate them into your applications using REST APIs or SDKs. Learn setting up resources, managing keys and endpoints, and calling AI services with code.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erstellen und Nutzen von Azure KI-Services',
          description:
            'Lernen Sie, wie Sie Azure AI-Dienste bereitstellen und über REST APIs oder SDKs in Ihre Anwendungen integrieren. Der Kurs zeigt, wie Sie Ressourcen einrichten, Schlüssel verwalten und KI-Funktionen programmatisch nutzen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/create_and_consume_azure_ai_services.webp',
      buttonType: 'URL',
      buttonURL: '/course/684aa067f0694ec85274b86b',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-004',
      translations: {
        en: {
          title: 'Secure Azure AI services',
          description:
            'Protect your Azure AI solutions by managing authentication and network security. Explore best practices to prevent data loss and ensure privacy in AI-powered applications.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Sicherung der Azure-KI-Services',
          description:
            'Lernen Sie, wie Sie Ihre Azure AI-Lösungen durch Authentifizierungs- und Netzwerksicherheit schützen. Dieser Kurs behandelt Best Practices zum Schutz vor Datenverlust und zur Sicherstellung der Privatsphäre.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/secure_azure_ai_services.jpeg',
      buttonType: 'URL',
      buttonURL: '/course/684bd8c2f0694ec85274e6fb',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-005',
      translations: {
        en: {
          title: 'Monitor Azure AI services',
          description:
            'Track performance, usage, and costs of Azure AI services to ensure reliable operation. Learn setting up alerts, viewing metrics, and managing diagnostic logs to help detect and resolve issues efficiently.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Überwachen von Azure KI Services',
          description:
            'Lernen Sie, wie Sie Leistung, Nutzung und Kosten Ihrer Azure AI-Dienste überwachen. Der Kurs zeigt, wie Sie Benachrichtigungen einrichten, Metriken anzeigen und Diagnoseprotokolle zur Fehlerbehebung nutzen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/monitor_azure_ai_services.webp',
      buttonType: 'URL',
      buttonURL: '/course/684bf4ebf0694ec85274fbe7',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-006',
      translations: {
        en: {
          title: 'Deploy Azure AI services in containers',
          description:
            'Run Azure AI services in Docker containers for greater flexibility and control over deployment. Learn creating, securing, and consuming AI services from containers in various environments.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Bereitstellen von Azure KI Services in Containern',
          description:
            'Lernen Sie, wie Sie Azure AI-Dienste in Docker-Containern ausführen und so flexible und kontrollierte Bereitstellungen ermöglichen. Der Kurs zeigt, wie Sie Container erstellen, absichern und KI-Dienste lokal konsumieren.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/deploy-azure-ai.webp',
      buttonType: 'URL',
      buttonURL: '/course/684aa275f0694ec85274b99c',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-007',
      translations: {
        en: {
          title: 'Explore Generative AI',
          description:
            'Dive into the fundamentals of generative AI, from NLP and LLMs to their impact on creativity and productivity. Learn more on how  generative AI services enhance creativity and productivity.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erkunden von generativer KI',
          description:
            'Tauchen Sie ein in die Grundlagen von generativer KI – von natürlicher Sprachverarbeitung (NLP) und grossen Sprachmodellen (LLMs) bis hin zu ihrem Einfluss auf Kreativität und Produktivität. Erfahren Sie, wie generative KI-Dienste die Kreativität und Effizienz steigern können.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/explore_generative_ai.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852943af0694ec8527572b7',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-008',
      translations: {
        en: {
          title: 'Get started with generative AI in Azure',
          description:
            'Build generative AI applications using the Azure AI Foundry portal. Explore available tools, models, and features to develop, test, and monitor AI solutions efficiently.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erste Schritte mit generativer KI in Azure',
          description:
            'Erstellen Sie generative KI-Anwendungen mit dem Azure AI Foundry-Portal. Entdecken Sie die verfügbaren Tools, Modelle und Funktionen, um KI-Lösungen effizient zu entwickeln, zu testen und zu überwachen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/get_started_with_generative_ai_in_azure.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852a1caf0694ec852757717',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-009',
      translations: {
        en: {
          title: 'Build Generative AI applications with Azure Cosmos DB',
          description:
            'Create generative AI apps using Python, Azure Cosmos DB for NoSQL, and Azure OpenAI. Integrate vector search, embeddings, and LangChain orchestration to develop scalable, intelligent AI solutions.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erstellen von generativen KI-Anwendungen mit Azure Cosmos DB',
          description:
            'Erstellen Sie generative KI-Anwendungen mit Python, Azure Cosmos DB für NoSQL und Azure OpenAI. Integrieren Sie Vektorsuche, Einbettungen und LangChain-Orchestrierung, um skalierbare, intelligente KI-Lösungen zu entwickeln.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/build_generative_ai_applications_with_azure_cosmos_db.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852a85cf0694ec8527577a2',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-009',
      translations: {
        en: {
          title: 'Manage GitHub Actions in the enterprise',
          description:
            'Manage GitHub Actions at scale with enterprise-level tools, runners, and security settings. Configure self-hosted runners, control access, and use encrypted secrets to support secure, customized workflows.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Verwalten von GitHub Actions im Unternehmen',
          description:
            'Verwalten Sie GitHub Actions in grossem Umfang mit Enterprise-Tools, Runnern und Sicherheitseinstellungen. Konfigurieren Sie selbstgehostete Runner, steuern Sie den Zugriff und verwenden Sie verschlüsselte Secrets, um sichere und angepasste Workflows zu unterstützen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/manage_github_actions_in_the_enterprise.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852d02cf0694ec852759386',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-010',
      translations: {
        en: {
          title: 'Leverage GitHub Actions to publish to GitHub Packages',
          description:
            'Automate the publishing of libraries and Docker images with GitHub Actions and GitHub Packages. Set up CI workflows, handle authentication, and manage your packages with ease.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Nutzen von GitHub Actions zum Veröffentlichen in GitHub Packages',
          description:
            'Automatisieren Sie das Veröffentlichen von Bibliotheken und Docker-Images mit GitHub Actions und GitHub Packages. Richten Sie CI-Workflows ein, verwalten Sie die Authentifizierung und behalten Sie Ihre Pakete mühelos im Griff.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/leverage_github_actions_to_publish_to_github_packages.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6853cbedf0694ec85275c081',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-011',
      translations: {
        en: {
          title: 'Build and deploy applications to Azure by using GitHub Actions',
          description:
            'Learn how to automate your deployment workflows with GitHub Actions and Azure. Set up secure CI/CD pipelines, manage credentials with GitHub Secrets, and deploy applications to Azure efficiently.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erstellen und Bereitstellen von Anwendungen in Azure mithilfe von GitHub Actions',
          description:
            'Erfahren Sie, wie Sie Ihre Bereitstellungs-Workflows mit GitHub Actions und Azure automatisieren. Richten Sie sichere CI/CD-Pipelines ein, verwalten Sie Anmeldeinformationen mit GitHub Secrets und stellen Sie Anwendungen effizient in Azure bereit.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/build_and_deploy_applications_to_azure_by_using_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/6852b5e4f0694ec852757d2f',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-012',
      translations: {
        en: {
          title: 'Manage end-to-end deployment scenarios by using Bicep and GitHub Actions',
          description:
            'Automate full-stack Azure deployments with Bicep and GitHub Actions by creating unified workflows for infrastructure, apps, and data while managing artifacts and passing data between steps.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Verwalten von End-to-End-Bereitstellungsszenarien mithilfe von Bicep und GitHub Actions',
          description:
            'Automatisieren Sie vollständige Azure-Bereitstellungen mit Bicep und GitHub Actions, indem Sie einheitliche Workflows für Infrastruktur, Anwendungen und Daten erstellen – inklusive Artefaktverwaltung und Datenaustausch zwischen den Schritten.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/manage_end_to_end_deployment_scenarios_by_using_bicep_and_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685b9984f0694ec852765319',
      newTab: false,
      locked: false,
    },
    {
      id: 'enterprise-013',
      translations: {
        en: {
          title: 'Build your first Bicep deployment workflow by using GitHub Actions',
          description:
            'Create your first automated Bicep deployment using GitHub Actions. Set up secure access, define workflow triggers, and deploy Azure resources with ease.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erstellen Sie Ihren ersten Bicep-Bereitstellungsworkflow mit GitHub-Aktionen',
          description:
            'Erstellen Sie Ihre erste automatisierte Bicep-Bereitstellung mit GitHub Actions. Richten Sie einen sicheren Zugriff ein, definieren Sie Workflow-Auslöser und stellen Sie Azure-Ressourcen mühelos bereit.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/build_your_first_bicep_deployment_workflow_by_using_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685b9d1df0694ec85276538e',
      newTab: false,
      locked: false,
    },
  ],
  'genai-cloud-cybersecurity': [
    {
      id: 'security-001',
      translations: {
        en: {
          title: 'Use AI responsibly with Azure AI Foundry Content Safety',
          description:
            "Detect and manage harmful content using Azure AI Foundry's Content Safety tools. This course covers how to apply text and image filters to identify violence, hate, self-harm, and other sensitive content in both user- and AI-generated material.",
          buttonText: 'Learn more',
        },
        de: {
          title: 'Verantwortungsvolles Verwenden von KI mit Azure KI Inhaltssicherheit',
          description:
            'Lernen Sie, wie Sie mit Azure AI Foundry Content Safety schädliche Inhalte erkennen und verwalten. Der Kurs behandelt Text- und Bildfilter zum Erkennen von Gewalt, Hass, Selbstverletzung und weiteren sensiblen Inhalten.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/use_ai_responsibly_with_azure_ai_foundry_content_safety.png',
      buttonType: 'URL',
      buttonURL: '/course/684aa4fdf0694ec85274ba2a',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-002',
      translations: {
        en: {
          title: 'Use an Azure Machine Learning job for automation',
          description:
            'Move your machine learning workflows into production using Azure ML jobs. Learn how to convert notebooks into scripts, define jobs with YAML, and run them using the CLI.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Verwenden eines Azure Machine Learning-Auftrags für die Automatisierung',
          description:
            'Lernen Sie, wie Sie Machine Learning-Workflows mit Azure ML-Jobs produktionsreif machen. Der Kurs zeigt, wie Sie Notebooks in Skripte umwandeln, Jobs mit YAML definieren und über CLI ausführen.',
          buttonText: 'Mehr erfahren',
        },
      },
      duration: '5h 35m',
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/use_an_azure_machine_learning_job_for_automation.jpeg',
      buttonType: 'URL',
      buttonURL: '/course/684bebf7f0694ec85274facf',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-003',
      translations: {
        en: {
          title: 'Trigger Azure Machine Learning jobs with GitHub Actions',
          description:
            'Automate your ML workflows by connecting GitHub Actions to Azure Machine Learning. Explore setting up credentials, storing secrets, and writing YAML workflows to run jobs securely and efficiently.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Auslösen von Azure Machine Learning-Aufträgen mit GitHub-Aktionen',
          description:
            'Lernen Sie, wie Sie GitHub Actions mit Azure Machine Learning verbinden, um ML-Workflows zu automatisieren. Der Kurs zeigt die Einrichtung sicherer Anmeldeinformationen, die Verwendung von Secrets und das Schreiben von YAML-Workflows.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/trigger_azure_machine_learning_jobs_with_github_actions.webp',
      buttonType: 'URL',
      buttonURL: '/course/684bf851f0694ec85274fc5a',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-004',
      translations: {
        en: {
          title: 'Trigger GitHub Actions with feature-based development',
          description:
            'Automate ML workflows by triggering GitHub Actions when code changes are merged. Learn feature-based development, branch protection, and creating pull request–based workflows for safer, more efficient deployments.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Auslösen von GitHub-Aktionen mit featurebasierter Entwicklung',
          description:
            'Lernen Sie, wie Sie GitHub Actions durch Codeänderungen automatisch auslösen. Der Kurs behandelt feature-basiertes Arbeiten, Branch-Schutz und Pull-Request-Workflows für sichere Deployments.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/trigger_github_actions_with_feature_based_development.jpg',
      buttonType: 'URL',
      buttonURL: '/course/684aad0af0694ec85274c314',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-005',
      translations: {
        en: {
          title: 'Work with linting and unit testing in GitHub Actions',
          description:
            'Automate code quality checks for ML projects using GitHub Actions. Learn running linters, integrating unit tests into pull requests, and troubleshooting issues to keep your code clean and reliable.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Arbeiten mit Linting und Unit-Tests in GitHub-Aktionen',
          description:
            'Lernen Sie, wie Sie mit GitHub Actions automatisierte Code-Qualitätsprüfungen für ML-Projekte einrichten. Der Kurs behandelt Linting, Unit-Tests und die Integration in Pull Requests.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/work_with_linting_and_unit_testing_in_github_actions.webp',
      buttonType: 'URL',
      buttonURL: '/course/684aa7e8f0694ec85274ba82',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-006',
      translations: {
        en: {
          title: 'Work with environments in GitHub Actions',
          description:
            'Manage machine learning workflows by using environments in GitHub Actions. Explore setting up environments, adding approval gates, and streamlining the training, testing, and deployment process.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Arbeiten mit Umgebungen in GitHub-Aktionen',
          description:
            'Lernen Sie, wie Sie ML-Workflows mit Umgebungen in GitHub Actions strukturieren und steuern. Der Kurs zeigt, wie Sie Umgebungen einrichten, Freigaben festlegen und Deployment-Prozesse optimieren.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/work_with_environments_in_github_actions.jpg',
      buttonType: 'URL',
      buttonURL: '/course/684bee2df0694ec85274fb27',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-007',
      translations: {
        en: {
          title: 'Deploy a model with GitHub Actions',
          description:
            'Automate the deployment of machine learning models using GitHub Actions and the Azure ML CLI. Learn deploying to a managed endpoint and testing the model after deployment.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Bereitstellen eines Modells mit GitHub-Aktionen',
          description:
            'Lernen Sie, wie Sie Machine Learning-Modelle mit GitHub Actions und dem Azure ML CLI automatisiert bereitstellen. Der Kurs begleitet Sie durch die Bereitstellung auf verwalteten Endpunkten und das Testen der Modelle.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/deploy_a_model_with_github_actions.png',
      buttonType: 'URL',
      buttonURL: '/course/68493e29f0694ec852748f8a',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-007',
      translations: {
        en: {
          title: 'Develop for Azure SQL Database',
          description:
            'Optimize your Azure SQL Database development by setting up projects, automating deployments with GitHub Actions or Azure DevOps, and implementing secure, monitored CI/CD pipelines.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Entwickeln für Azure SQL-Datenbank',
          description:
            'Optimieren Sie Ihre Azure SQL-Datenbankentwicklung, indem Sie Projekte einrichten, Bereitstellungen mit GitHub Actions oder Azure DevOps automatisieren und sichere, überwachte CI/CD-Pipelines implementieren.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/develop_for_azure_sql_database.jpg',
      buttonType: 'URL',
      buttonURL: '/course/68540684f0694ec85275cbaa',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-008',
      translations: {
        en: {
          title: 'Register an MLflow model in Azure Machine Learning',
          description:
            'Log, register, and manage your machine learning models with MLflow in Azure Machine Learning. Understand the MLmodel format to streamline model tracking and deployment.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Registrieren eines MLflow-Modells in Azure Machine Learning',
          description:
            'Protokollieren, registrieren und verwalten Sie Ihre Machine-Learning-Modelle mit MLflow in Azure Machine Learning. Verstehen Sie das MLmodel-Format, um die Nachverfolgung und Bereitstellung von Modellen zu vereinfachen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/register_an_mlflow_model_in_azure_machine_learning.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685bb47bf0694ec85276583b',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-009',
      translations: {
        en: {
          title: 'Create and explore the Responsible AI dashboard for a model in Azure Machine Learning',
          description:
            'Build and explore a Responsible AI dashboard in Azure Machine Learning to analyze model behavior using explanations, error analysis, and causal insights.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Erstellen und Erkunden des Dashboards für verantwortungsvolle KI für ein Modell in Azure Machine Learning',
          description:
            'Erstellen und erkunden Sie ein Responsible-AI-Dashboard in Azure Machine Learning, um das Modellverhalten anhand von Erklärungen, Fehleranalysen und kausalen Erkenntnissen zu analysieren.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/create_and_explore_the_responsible_ai_dashboard_for_a_model_in_azure_machine_learning.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685bbb48f0694ec852765987',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-010',
      translations: {
        en: {
          title: 'Deploy a model to a managed online endpoint',
          description:
            'Deploy machine learning models for real-time inference using Azure\'s managed online endpoints. Learn how to use MLflow or custom models and test their performance in a live environment.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Bereitstellen eines Modells auf einem verwalteten Onlineendpunkt',
          description:
            'Stellen Sie Machine-Learning-Modelle für Echtzeit-Inferenzen mit den verwalteten Online-Endpunkten von Azure bereit. Erfahren Sie, wie Sie MLflow- oder benutzerdefinierte Modelle verwenden und deren Leistung in einer Live-Umgebung testen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/deploy_a_model_to_a_managed_online_endpoint.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685e94f36ffc600e04cd724d',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-011',
      translations: {
        en: {
          title: 'Deploy a model to a batch endpoint',
          description:
            'Deploy ML models for large-scale processing using Azure batch endpoints. Learn how to create, deploy, and invoke MLflow or custom models for batch scoring jobs.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Bereitstellen eines Modells an einem Batchendpunkt',
          description:
            'Stellen Sie ML-Modelle für die Verarbeitung im grossen Stil mit Azure-Batch-Endpunkten bereit. Erfahren Sie, wie Sie MLflow- oder benutzerdefinierte Modelle für Batch-Scoring-Aufgaben erstellen, bereitstellen und aufrufen.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/deploy_a_model_to_a_batch_endpoint.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685e952b6ffc600e04cd7262',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-012',
      translations: {
        en: {
          title: 'Secure your hybrid and multicloud machines by using Azure Arc-enabled servers',
          description:
            'Discover how to onboard Azure Arc-enabled servers to Microsoft Defender for Cloud and Microsoft Sentinel and explore the security benefits of Defender for Servers across hybrid environments.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Schützen Sie Ihre hybriden und Multi-Cloud-Computer durch Verwendung von Azure Arc-fähigen Servern',
          description:
            'Erfahren Sie, wie Sie Azure Arc-fähige Server in Microsoft Defender for Cloud und Microsoft Sentinel einbinden und welche Sicherheitsvorteile Defender for Servers in hybriden Umgebungen bietet.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/secure_your_hybrid_and_multicloud_machines_by_using_azure_arc_enabled_servers.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685e95566ffc600e04cd727a',
      newTab: false,
      locked: false,
    },
    {
      id: 'security-013',
      translations: {
        en: {
          title: 'Replicate and migrate virtual machine servers with Azure Migrate',
          description: 'Replicate, test, and migrate virtual machines to production using Azure Migrate. Understand each step of the migration process from replication to deployment.',
          buttonText: 'Learn more',
        },
        de: {
          title: 'Replizieren und Migrieren von virtuellen Computerservern mit Azure Migrate',
          description:
            'Replizieren, testen und migrieren Sie virtuelle Maschinen mit Azure Migrate in die Produktionsumgebung. Verstehen Sie jeden Schritt des Migrationsprozesses – von der Replikation bis zur Bereitstellung.',
          buttonText: 'Mehr erfahren',
        },
      },
      imageSrc:
        'https://aibscontentstorage.blob.core.windows.net/content-files/Common/replicate_and_migrate_virtual_machine_servers_with_azure_migrate.jpg',
      buttonType: 'URL',
      buttonURL: '/course/685e95ff6ffc600e04cd7293',
      newTab: false,
      locked: false,
    },
  ],
};

export const technicalSpecialistTrainings = [
  {
    id: 'tech-spec-001',
    image: 'https://aibsassets.blob.core.windows.net/img/core-ai-essentials.jpg',
    title: 'Core AI Essentials: Machine and Deep Learning',
    description:
      ' Lay the foundation with a deep dive into classical AI techniques. Explore core supervised and unsupervised algorithms, neural network architectures, optimization methods, and best practices in model evaluation and feature engineering.',
    lock: false,
    visible: true,
    slug: 'core-ai-essentials',
    translation: {
      en: {
        title: 'Core AI Essentials: Machine and Deep Learning',
        description:
          'Lay the foundation with a deep dive into classical AI techniques. Explore core supervised and unsupervised algorithms, neural network architectures, optimization methods, and best practices in model evaluation and feature engineering.',
      },
      de: {
        title: 'AI Grundlagen: Maschinelles Lernen und Deep Learning',
        description:
          'Legen Sie den Grundstein mit tiefen Einblicken in klassische AI-Techniken. Entdecken Sie Supervised und Unsupervised Algorithmen, neuronale Netzwerkarchitekturen, Optimierungsmethoden und Best Practices in der Modellbewertung und im Feature Engineering.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 1,
      rules: [
        {
          order: 1,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-002',
    image:
      'https://aibsassets.blob.core.windows.net/img/mastering-prompt-engineering-and-llm-fine-tuning.jpg',
    title: 'Mastering Prompt Engineering and LLM Fine-Tuning',
    description:
      'Learn to craft effective prompts, apply chain-of-thought strategies, and perform targeted fine-tuning.',
    lock: false,
    visible: true,
    slug: 'prompt-engineering-llm-fine-tuning',
    translation: {
      en: {
        title: 'Mastering Prompt Engineering and LLM Fine-Tuning',
        description:
          'Learn to craft effective prompts, apply chain-of-thought strategies, and perform targeted fine-tuning on pre-trained large language models—maximizing relevance, accuracy, and reliability in your AI-driven applications.',
      },
      de: {
        title: 'Prompt Engineering und LLM Fine-Tuning meistern',
        description:
          'Lernen Sie, effektive Prompts zu erstellen, Chain-of-Thought Strategien anzuwenden und vortrainierte große Sprachmodelle gezielt zu optimieren – für maximale Relevanz, Genauigkeit und Zuverlässigkeit Ihrer AI-gesteuerten Anwendungen.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 2,
      rules: [
        {
          order: 2,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-003',
    image:
      'https://aibscontentstorage.blob.core.windows.net/content-files/Common/integrating_genai_into_software_engineering_workflows.jpg',
    title: 'Integrating GenAI into Software Engineering Workflows',
    description: 'Seamlessly embed generative AI into your development lifecycle.',
    lock: false,
    visible: true,
    slug: 'genai-software-engineering-workflows',
    translation: {
      en: {
        title: 'Integrating GenAI into Software Engineering Workflows',
        description:
          'Seamlessly embed generative AI into your development lifecycle: from IDE plugins and code-completion tools to automated pull-request reviews and CI/CD pipelines, accelerating delivery while ensuring code quality.',
      },
      de: {
        title: 'GenAI in Software-Engineering-Workflows integrieren',
        description:
          'Integrieren Sie generative AI nahtlos in Ihren Entwicklungslebenszyklus: von IDE-Plugins und Code-Vervollständigungstools bis hin zu automatisierten Pull-Request-Reviews und CI/CD-Pipelines, um die Bereitstellung zu beschleunigen und gleichzeitig die Codequalität sicherzustellen.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 3,
      rules: [
        {
          order: 3,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-004',
    image: 'https://aibsassets.blob.core.windows.net/img/ai-agents.jpg',
    title: 'Designing and Deploying Autonomous AI Agents',
    description:
      'Architect self-governing agents capable of multi-step reasoning and dynamic decision-making.',
    lock: false,
    visible: true,
    slug: 'autonomous-ai-agents',
    translation: {
      en: {
        title: 'Designing and Deploying Autonomous AI Agents',
        description:
          'Architect self-governing agents capable of multi-step reasoning and dynamic decision-making. Cover agent frameworks, orchestration patterns, safety constraints, and real-world deployment strategies for autonomous workflows.',
      },
      de: {
        title: 'Autonome AI Agenten entwerfen und einsetzen',
        description:
          'Entwickeln Sie autonome Agenten, die mehrstufiges Denken und dynamische Entscheidungsfindung ermöglichen. Befassen Sie sich mit Agenten-Frameworks, Orchestrierungsmustern, Sicherheitsbeschränkungen und praxisnahen Bereitstellungsstrategien für autonome Workflows.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 4,
      rules: [
        {
          order: 4,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-005',
    image: 'https://aibsassets.blob.core.windows.net/img/genai-enterprise.jpg',
    title: 'Scaling and Managing GenAI Solutions in Enterprise Environments',
    description:
      'Build robust, enterprise-grade GenAI platforms with a focus on scalability and governance.',
    lock: false,
    visible: true,
    slug: 'scaling-genai-enterprise',
    translation: {
      en: {
        title: 'Scaling and Managing GenAI Solutions in Enterprise Environments',
        description:
          'Build robust, enterprise-grade GenAI platforms with a focus on scalability, observability, cost optimization, and governance. Master containerization, orchestration (Kubernetes), monitoring stacks, and policy-driven compliance.',
      },
      de: {
        title: 'Skalierung und Management von GenAI in Unternehmensumgebungen',
        description:
          'Erstellen Sie robuste GenAI-Plattformen auf Unternehmensniveau mit Fokus auf Skalierbarkeit, Beobachtbarkeit, Kostenoptimierung und Governance. Meistern Sie Containerisierung, Orchestrierung (Kubernetes), Monitoring-Stacks und richtliniengesteuerte Compliance.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 5,
      rules: [
        {
          order: 5,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-006',
    image: 'https://aibsassets.blob.core.windows.net/img/genai-cloud.jpg',
    title: 'Applying GenAI in Cloud Infrastructure and Cybersecurity',
    description:
      'Harness generative models to enhance threat detection and automate incident response.',
    lock: false,
    visible: true,
    slug: 'genai-cloud-cybersecurity',
    translation: {
      en: {
        title: 'Applying GenAI in Cloud Infrastructure and Cybersecurity',
        description:
          'Harness generative models to enhance threat detection, automate incident response, and enforce secure infrastructure-as-code. Combine AI-driven anomaly detection with cloud-native security frameworks for resilient, self-healing systems.',
      },
      de: {
        title: 'GenAI in Cloud-Infrastrukturen und Cybersicherheit',
        description:
          'Nutzen Sie generative Modelle, um die Bedrohungserkennung zu verbessern, die Reaktion auf Vorfälle zu automatisieren und um sichere Infrastructure-as-Code durchzusetzen. Kombinieren Sie AI-gesteuerte Anomalie-Erkennung mit Cloud-nativen Sicherheits-Frameworks für robuste, selbstheilende Systeme.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 6,
      rules: [
        {
          order: 6,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
];
