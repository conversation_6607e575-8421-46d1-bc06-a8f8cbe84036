const express = require("express");
const router = express.Router();
const FormController = require("../../controllers/FormController");

const apiKeyMiddleware = require("../../middlewares/apiKeyMiddleware");
const authHandler = require("../../middlewares/authHandler");
const validatePagination = require("../../middlewares/validatePagination");
router.use(apiKeyMiddleware);
router.use(authHandler);

router.post("/create", FormController.createForm);
router.get("/list", validatePagination, FormController.getForms);
router.get("/list/:id", FormController.getFormById);


router.put("/update/:id", FormController.updateForm);
router.delete("/delete/:id", FormController.deleteForm);

// Çeviri desteği rotaları
router.get("/:id/translations", FormController.getFormTranslations);
router.get("/:id/translations/:language", FormController.getFormTranslation);
router.put("/:id/translations/:language", FormController.updateFormTranslation);
router.delete(
  "/:id/translations/:language",
  FormController.deleteFormTranslation
);

module.exports = router;
