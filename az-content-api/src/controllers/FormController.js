const FormModel = require("../mongodb_models/FormModel");
const { httpResponse } = require("../utils/helpers");
const ENUM = require("../utils/enum");

exports.createForm = async (req, res) => {
  try {
    const { title, description, fields, topics } = req.body;
    const form = new FormModel({
      title,
      description,
      fields: fields || [],
      topics: topics || [],
      createdBy: req.user._id,
    });

    const savedForm = await form.save();
    return httpResponse(
      res,
      ENUM.HTTP_CODES.CREATED,
      "success",
      "Form created successfully",
      savedForm
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred while creating form",
      error.message
    );
  }
};

exports.getForms = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      form_type,
      form_id,
      title,
    } = req.query;
    const query = status ? { status } : {};

    if (form_type) {
      query.form_type = form_type;
    }

    if (form_id) {
      query.form_id = form_id;
    }

    if (title) {
      // Search in main title and translations
      query.$or = [
        { title: { $regex: title, $options: "i" } }, // Main title
        { "translations.en.title": { $regex: title, $options: "i" } }, // English translation
        { "translations.de.title": { $regex: title, $options: "i" } }, // German translation
        { "translations.fr.title": { $regex: title, $options: "i" } }, // French translation
        { "translations.es.title": { $regex: title, $options: "i" } }, // Spanish translation
      ];
    }

    const forms = await FormModel.find(query)
      .skip((page - 1) * limit)
      .limit(limit)
      .populate("createdBy", "name email");

    const total = await FormModel.countDocuments(query);

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Forms retrieved successfully",
      {
        forms,
        pagination: {
          total,
          page: Number(page),
          pages: Math.ceil(total / limit),
        },
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred while retrieving forms",
      error.message
    );
  }
};

exports.getFormById = async (req, res) => {
  try {
    const { id } = req.params;
    const { lang } = req.query;

    const form = await FormModel.findById(id).populate(
      "createdBy",
      "name email"
    );

    if (!form) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Form not found",
        null
      );
    }

    // Eğer lang parametresi varsa ve o dilde çeviri mevcutsa
    if (lang && form.translations && form.translations.has(lang)) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.OK,
        "success",
        "Form translation retrieved successfully",
        form.translations.get(lang)
      );
    }

    // Eğer lang parametresi varsa ama çeviri yoksa
    if (lang) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        `Translation not found for lang: ${lang}`,
        null
      );
    }

    // Eğer lang parametresi yoksa orijinal form'u gönder
    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Form retrieved successfully",
      form
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred while retrieving the form",
      error.message
    );
  }
};

exports.updateForm = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    updateData.updatedAt = Date.now();

    // Form verilerinin MongoDB modeline uygun hale getirilmesi

    // Temizlenmiş veriyi oluştur
    const cleanedData = { ...updateData };

    // Topics alanını düzgün şekilde işle
    if (cleanedData.topics) {
      // Topics bir object ise (dizi değilse) diziye dönüştür
      if (!Array.isArray(cleanedData.topics)) {
        cleanedData.topics = [cleanedData.topics];
      }

      // Her bir topic öğesini temizle
      cleanedData.topics = cleanedData.topics.map((topic) => {
        // Geçici ID'leri temizle
        const cleanTopic = { ...topic };

        // Geçici topic ID'sini temizle
        if (
          cleanTopic._id &&
          typeof cleanTopic._id === "string" &&
          cleanTopic._id.startsWith("temp_")
        ) {
          delete cleanTopic._id;
        }

        // Topic'in name alanını kontrol et
        if (!cleanTopic.title) {
          cleanTopic.title = "Untitled Topic";
        }

        // Fields dizisini kontrol et
        if (cleanTopic.fields) {
          if (!Array.isArray(cleanTopic.fields)) {
            cleanTopic.fields = [cleanTopic.fields];
          }

          // Her bir field'ı temizle
          cleanTopic.fields = cleanTopic.fields.map((field) => {
            const cleanField = { ...field };

            // Geçici ID'leri temizle
            if (
              cleanField._id &&
              ((typeof cleanField._id === "string" &&
                cleanField._id.startsWith("temp_")) ||
                (typeof cleanField._id.toString === "function" &&
                  cleanField._id.toString().startsWith("temp_")))
            ) {
              delete cleanField._id;
            }

            // Validation alanını temizle
            if (cleanField.validation) {
              // Boş validation değerlerini temizle
              const cleanValidation = {};
              if (
                cleanField.validation.min !== null &&
                cleanField.validation.min !== ""
              )
                cleanValidation.min = cleanField.validation.min;
              if (
                cleanField.validation.max !== null &&
                cleanField.validation.max !== ""
              )
                cleanValidation.max = cleanField.validation.max;
              if (
                cleanField.validation.pattern !== null &&
                cleanField.validation.pattern !== ""
              )
                cleanValidation.pattern = cleanField.validation.pattern;
              if (
                cleanField.validation.message !== null &&
                cleanField.validation.message !== ""
              )
                cleanValidation.message = cleanField.validation.message;

              // Validation alanını güncelle
              if (Object.keys(cleanValidation).length > 0) {
                cleanField.validation = cleanValidation;
              } else {
                delete cleanField.validation;
              }
            }

            // Conditional logic alanını temizle
            if (cleanField.conditional_logic) {
              if (!cleanField.conditional_logic.enabled) {
                cleanField.conditional_logic = { enabled: false };
              } else if (cleanField.conditional_logic.rules) {
                // Rules alanını temizle
                if (!Array.isArray(cleanField.conditional_logic.rules)) {
                  cleanField.conditional_logic.rules = [
                    [
                      {
                        field: "",
                        operator: "has_any_value",
                        value: "",
                      },
                    ],
                  ];
                }
              }
            }

            // Options dizisini kontrol et
            if (cleanField.options) {
              if (!Array.isArray(cleanField.options)) {
                cleanField.options = [];
              } else {
                cleanField.options = cleanField.options.map((option) => {
                  const cleanOption = { ...option };
                  if (!cleanOption.label) cleanOption.label = "";
                  if (!cleanOption.value) cleanOption.value = "";
                  return cleanOption;
                });
              }
            } else {
              cleanField.options = [];
            }

            // Min/max değerlerini temizle
            if (cleanField.min === null || cleanField.min === "")
              delete cleanField.min;
            if (cleanField.max === null || cleanField.max === "")
              delete cleanField.max;

            // fields dizisi boşsa temizle
            if (cleanField.fields && cleanField.fields.length === 0)
              delete cleanField.fields;

            return cleanField;
          });
        } else {
          cleanTopic.fields = [];
        }

        return cleanTopic;
      });
    }

    const updatedForm = await FormModel.findByIdAndUpdate(id, cleanedData, {
      new: true,
    });

    if (!updatedForm) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Form not found"
      );
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Form updated successfully",
      updatedForm
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred while updating form",
      error.message
    );
  }
};

exports.deleteForm = async (req, res) => {
  try {
    const { id } = req.params;
    const deletedForm = await FormModel.findByIdAndDelete(id);

    if (!deletedForm) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Form not found"
      );
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Form deleted successfully"
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred while deleting form",
      error.message
    );
  }
};

exports.validateFormData = async (req, res) => {
  try {
    const { formId, formData } = req.body;
    const form = await FormModel.findById(formId);

    if (!form) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Form not found"
      );
    }

    const validationErrors = [];
    const processFields = (fields, data, parentField = "") => {
      fields.forEach((field) => {
        if (field.conditional_logic && field.conditional_logic.enabled) {
          const shouldShow = evaluateConditionalLogic(
            field.conditional_logic,
            data
          );
          if (!shouldShow) return;
        }

        if (field.required && !data[field.name]) {
          validationErrors.push(`${field.label || field.name} is required`);
        }

        if (data[field.name]) {
          switch (field.type) {
            case "email":
              if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data[field.name])) {
                validationErrors.push(
                  `${field.label || field.name} must be a valid email`
                );
              }
              break;
            case "number":
              if (
                field.validation.min &&
                data[field.name] < field.validation.min
              ) {
                validationErrors.push(
                  `${field.label || field.name} must be greater than ${
                    field.validation.min
                  }`
                );
              }
              if (
                field.validation.max &&
                data[field.name] > field.validation.max
              ) {
                validationErrors.push(
                  `${field.label || field.name} must be less than ${
                    field.validation.max
                  }`
                );
              }
              break;
            case "feedback":
              const feedbackValue = parseInt(data[field.name], 10);
              if (
                isNaN(feedbackValue) ||
                feedbackValue < 0 ||
                feedbackValue > 10
              ) {
                validationErrors.push(
                  `${
                    field.label || field.name
                  } must be a number between 0 and 10`
                );
              }
              break;
            case "repeater":
              if (Array.isArray(data[field.name])) {
                if (field.min && data[field.name].length < field.min) {
                  validationErrors.push(
                    `${field.label || field.name} must have at least ${
                      field.min
                    } items`
                  );
                }
                if (field.max && data[field.name].length > field.max) {
                  validationErrors.push(
                    `${field.label || field.name} must have at most ${
                      field.max
                    } items`
                  );
                }
                data[field.name].forEach((item, index) => {
                  processFields(field.fields, item, `${field.name}[${index}]`);
                });
              }
              break;
            case "group":
              if (field.fields) {
                processFields(field.fields, data[field.name] || {}, field.name);
              }
              break;
          }
        }
      });
    };

    processFields(form.fields, formData);

    return httpResponse(
      res,
      validationErrors.length
        ? ENUM.HTTP_CODES.BAD_REQUEST
        : ENUM.HTTP_CODES.OK,
      validationErrors.length ? "error" : "success",
      validationErrors.length ? "Validation failed" : "Validation successful",
      { errors: validationErrors }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred during validation",
      error.message
    );
  }
};

const evaluateConditionalLogic = (conditionalLogic, formData) => {
  return conditionalLogic.rules.some((ruleGroup) => {
    return ruleGroup.every((rule) => {
      const fieldValue = formData[rule.field];

      switch (rule.operator) {
        case "has_any_value":
          return fieldValue !== undefined && fieldValue !== "";
        case "is_empty":
          return !fieldValue;
        case "==":
          return fieldValue == rule.value;
        case "!=":
          return fieldValue != rule.value;
        case ">":
          return Number(fieldValue) > Number(rule.value);
        case "<":
          return Number(fieldValue) < Number(rule.value);
        case "in":
          if (!rule.value) return false;
          const valueList = rule.value.split(",").map((v) => v.trim());
          return valueList.includes(String(fieldValue));
        case "not_in":
          if (!rule.value) return false;
          const excludeList = rule.value.split(",").map((v) => v.trim());
          return !excludeList.includes(String(fieldValue));
        case "contains":
          return String(fieldValue).includes(rule.value);
        case "starts_with":
          return String(fieldValue).startsWith(rule.value);
        case "ends_with":
          return String(fieldValue).endsWith(rule.value);
        default:
          return false;
      }
    });
  });
};

// Form çevirilerini alma
exports.getFormTranslations = async (req, res) => {
  try {
    const { id } = req.params;
    const form = await FormModel.findById(id);

    if (!form) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Form not found",
        null
      );
    }

    const translations = form.translations
      ? Object.fromEntries(form.translations)
      : {};

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Form translations retrieved successfully",
      translations
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred while retrieving form translations",
      error.message
    );
  }
};

// Belirli bir dildeki form çevirisini alma
exports.getFormTranslation = async (req, res) => {
  try {
    const { id, language } = req.params;
    const form = await FormModel.findById(id);

    if (!form) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Form not found",
        null
      );
    }

    if (!form.translations || !form.translations.has(language)) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        `Translation for language '${language}' not found`,
        null
      );
    }

    const translation = form.translations.get(language);

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Form translation retrieved successfully",
      translation
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred while retrieving form translation",
      error.message
    );
  }
};

// Form çevirisini güncelleme
exports.updateFormTranslation = async (req, res) => {
  try {
    const { id, language } = req.params;
    const translation = req.body;

    const form = await FormModel.findById(id);
    if (!form) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Form not found",
        null
      );
    }

    // Form çevirilerini güncelle
    if (!form.translations) {
      form.translations = new Map();
    }

    // Orijinal form yapısına uygun şekilde çeviri nesnesini düzenle
    if (translation.topics && Array.isArray(translation.topics)) {
      translation.topics.forEach((translatedTopic, topicIndex) => {
        // Eğer orijinal form'da karşılık gelen topic yoksa işlemi atla
        if (!form.topics || !form.topics[topicIndex]) return;

        const originalTopic = form.topics[topicIndex];

        // _id alanını koru
        if (originalTopic._id && !translatedTopic._id) {
          translatedTopic._id = originalTopic._id.toString();
        }

        // fields dizisini kontrol et
        if (translatedTopic.fields && Array.isArray(translatedTopic.fields)) {
          translatedTopic.fields.forEach((translatedField, fieldIndex) => {
            // Eğer orijinal topic'te karşılık gelen field yoksa işlemi atla
            if (!originalTopic.fields || !originalTopic.fields[fieldIndex])
              return;

            const originalField = originalTopic.fields[fieldIndex];

            // Eksik alanları orijinal alandan al
            if (!translatedField.name)
              translatedField.name = originalField.name;
            if (!translatedField.type)
              translatedField.type = originalField.type;
            if (originalField.required !== undefined)
              translatedField.required = originalField.required;

            // _id alanını koru
            if (originalField._id && !translatedField._id) {
              translatedField._id = originalField._id.toString();
            }

            // conditional_logic alanını koru
            if (
              originalField.conditional_logic &&
              !translatedField.conditional_logic
            ) {
              translatedField.conditional_logic =
                originalField.conditional_logic;
            }

            // fields dizisini koru (repeater veya group için)
            if (originalField.fields && !translatedField.fields) {
              translatedField.fields = originalField.fields;
            }
          });
        }
      });
    }

    form.translations.set(language, translation);
    await form.save();

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Form translation updated successfully",
      { formId: id, language }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred while updating form translation",
      error.message
    );
  }
};

// Form çevirisini silme
exports.deleteFormTranslation = async (req, res) => {
  try {
    const { id, language } = req.params;
    const form = await FormModel.findById(id);

    if (!form) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Form not found",
        null
      );
    }

    if (!form.translations || !form.translations.has(language)) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        `Translation for language '${language}' not found`,
        null
      );
    }

    form.translations.delete(language);
    await form.save();

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Form translation deleted successfully",
      { formId: id, language }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An error occurred while deleting form translation",
      error.message
    );
  }
};


